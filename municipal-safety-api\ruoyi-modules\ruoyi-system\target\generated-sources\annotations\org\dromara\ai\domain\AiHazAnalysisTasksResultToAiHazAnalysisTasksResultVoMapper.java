package org.dromara.ai.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.ai.domain.bo.AiHazAnalysisTasksResultBoToAiHazAnalysisTasksResultMapper;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksResultVo;
import org.dromara.ai.domain.vo.AiHazAnalysisTasksResultVoToAiHazAnalysisTasksResultMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {AiHazAnalysisTasksResultBoToAiHazAnalysisTasksResultMapper.class,AiHazAnalysisTasksResultVoToAiHazAnalysisTasksResultMapper.class},
    imports = {}
)
public interface AiHazAnalysisTasksResultToAiHazAnalysisTasksResultVoMapper extends BaseMapper<AiHazAnalysisTasksResult, AiHazAnalysisTasksResultVo> {
}
