package org.dromara.quality.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.quality.domain.QualityDevice;
import org.dromara.quality.domain.QualityDeviceToQualityDeviceVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {QualityDeviceToQualityDeviceVoMapper.class},
    imports = {}
)
public interface QualityDeviceVoToQualityDeviceMapper extends BaseMapper<QualityDeviceVo, QualityDevice> {
}
