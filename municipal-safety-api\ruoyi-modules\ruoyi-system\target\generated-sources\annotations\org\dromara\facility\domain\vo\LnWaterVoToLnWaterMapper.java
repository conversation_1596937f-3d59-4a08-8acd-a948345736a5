package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.LnWater;
import org.dromara.facility.domain.LnWaterToLnWaterVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {LnWaterToLnWaterVoMapper.class},
    imports = {}
)
public interface LnWaterVoToLnWaterMapper extends BaseMapper<LnWaterVo, LnWater> {
}
