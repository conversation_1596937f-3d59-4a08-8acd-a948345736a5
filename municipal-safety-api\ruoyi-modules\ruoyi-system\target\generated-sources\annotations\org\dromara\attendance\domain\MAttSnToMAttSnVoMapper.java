package org.dromara.attendance.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.attendance.domain.bo.MAttSnBoToMAttSnMapper;
import org.dromara.attendance.domain.vo.MAttSnVo;
import org.dromara.attendance.domain.vo.MAttSnVoToMAttSnMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {MAttSnBoToMAttSnMapper.class,MAttSnVoToMAttSnMapper.class},
    imports = {}
)
public interface MAttSnToMAttSnVoMapper extends BaseMapper<MAttSn, MAttSnVo> {
}
