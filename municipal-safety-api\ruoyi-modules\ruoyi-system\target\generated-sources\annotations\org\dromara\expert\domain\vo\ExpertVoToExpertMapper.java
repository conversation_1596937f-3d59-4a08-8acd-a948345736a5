package org.dromara.expert.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.expert.domain.Expert;
import org.dromara.expert.domain.ExpertToExpertVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {ExpertToExpertVoMapper.class},
    imports = {}
)
public interface ExpertVoToExpertMapper extends BaseMapper<ExpertVo, Expert> {
}
