package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.LnSmoke;
import org.dromara.facility.domain.LnSmokeToLnSmokeVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {LnSmokeToLnSmokeVoMapper.class},
    imports = {}
)
public interface LnSmokeVoToLnSmokeMapper extends BaseMapper<LnSmokeVo, LnSmoke> {
}
