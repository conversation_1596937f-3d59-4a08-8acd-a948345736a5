package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.LnWaterVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnWaterToLnWaterVoMapperImpl implements LnWaterToLnWaterVoMapper {

    @Override
    public LnWaterVo convert(LnWater arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnWaterVo lnWaterVo = new LnWaterVo();

        lnWaterVo.setId( arg0.getId() );
        lnWaterVo.setSdds( arg0.getSdds() );
        lnWaterVo.setRecordTime( arg0.getRecordTime() );
        lnWaterVo.setSylj( arg0.getSylj() );
        lnWaterVo.setZrlj( arg0.getZrlj() );
        lnWaterVo.setQyxslj( arg0.getQyxslj() );
        lnWaterVo.setDqxsnlj( arg0.getDqxsnlj() );
        lnWaterVo.setSsls( arg0.getSsls() );
        lnWaterVo.setDczt( arg0.getDczt() );
        lnWaterVo.setDcdy( arg0.getDcdy() );
        lnWaterVo.setDcdl( arg0.getDcdl() );
        lnWaterVo.setBl( arg0.getBl() );
        lnWaterVo.setDevNo( arg0.getDevNo() );
        lnWaterVo.setCreateTime( arg0.getCreateTime() );

        return lnWaterVo;
    }

    @Override
    public LnWaterVo convert(LnWater arg0, LnWaterVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setSdds( arg0.getSdds() );
        arg1.setRecordTime( arg0.getRecordTime() );
        arg1.setSylj( arg0.getSylj() );
        arg1.setZrlj( arg0.getZrlj() );
        arg1.setQyxslj( arg0.getQyxslj() );
        arg1.setDqxsnlj( arg0.getDqxsnlj() );
        arg1.setSsls( arg0.getSsls() );
        arg1.setDczt( arg0.getDczt() );
        arg1.setDcdy( arg0.getDcdy() );
        arg1.setDcdl( arg0.getDcdl() );
        arg1.setBl( arg0.getBl() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
