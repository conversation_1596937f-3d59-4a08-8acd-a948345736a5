package org.dromara.projects.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.monito.domain.DeviceMonitoToDeviceMonitoVoMapper;
import org.dromara.monito.domain.vo.DeviceMonitoVoToDeviceMonitoMapper;
import org.dromara.projects.domain.PrjPersonnelToPrjPersonnelVoMapper;
import org.dromara.projects.domain.PrjProjects;
import org.dromara.projects.domain.PrjProjectsToPrjProjectsVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {PrjPersonnelVoToPrjPersonnelMapper.class,PrjPersonnelToPrjPersonnelVoMapper.class,DeviceMonitoVoToDeviceMonitoMapper.class,DeviceMonitoToDeviceMonitoVoMapper.class,PrjProjectsToPrjProjectsVoMapper.class},
    imports = {}
)
public interface PrjProjectsVoToPrjProjectsMapper extends BaseMapper<PrjProjectsVo, PrjProjects> {
}
