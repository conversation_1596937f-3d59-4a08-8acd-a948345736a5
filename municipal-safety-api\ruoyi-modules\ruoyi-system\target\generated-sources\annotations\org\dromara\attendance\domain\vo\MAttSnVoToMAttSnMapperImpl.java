package org.dromara.attendance.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.MAttSn;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttSnVoToMAttSnMapperImpl implements MAttSnVoToMAttSnMapper {

    @Override
    public MAttSn convert(MAttSnVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttSn mAttSn = new MAttSn();

        mAttSn.setSnId( arg0.getSnId() );
        mAttSn.setProjectId( arg0.getProjectId() );
        mAttSn.setSn( arg0.getSn() );
        mAttSn.setSnName( arg0.getSnName() );
        mAttSn.setDirection( arg0.getDirection() );
        mAttSn.setStatus( arg0.getStatus() );

        return mAttSn;
    }

    @Override
    public MAttSn convert(MAttSnVo arg0, MAttSn arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSnId( arg0.getSnId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setSn( arg0.getSn() );
        arg1.setSnName( arg0.getSnName() );
        arg1.setDirection( arg0.getDirection() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
