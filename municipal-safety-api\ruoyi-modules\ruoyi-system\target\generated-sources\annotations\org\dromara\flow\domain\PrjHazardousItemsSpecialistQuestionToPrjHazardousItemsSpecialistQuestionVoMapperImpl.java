package org.dromara.flow.domain;

import javax.annotation.processing.Generated;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistQuestionVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsSpecialistQuestionToPrjHazardousItemsSpecialistQuestionVoMapperImpl implements PrjHazardousItemsSpecialistQuestionToPrjHazardousItemsSpecialistQuestionVoMapper {

    @Override
    public PrjHazardousItemsSpecialistQuestionVo convert(PrjHazardousItemsSpecialistQuestion arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsSpecialistQuestionVo prjHazardousItemsSpecialistQuestionVo = new PrjHazardousItemsSpecialistQuestionVo();

        prjHazardousItemsSpecialistQuestionVo.setId( arg0.getId() );
        prjHazardousItemsSpecialistQuestionVo.setName( arg0.getName() );
        prjHazardousItemsSpecialistQuestionVo.setDetail( arg0.getDetail() );
        prjHazardousItemsSpecialistQuestionVo.setResultId( arg0.getResultId() );
        prjHazardousItemsSpecialistQuestionVo.setSpecialist( arg0.getSpecialist() );

        return prjHazardousItemsSpecialistQuestionVo;
    }

    @Override
    public PrjHazardousItemsSpecialistQuestionVo convert(PrjHazardousItemsSpecialistQuestion arg0, PrjHazardousItemsSpecialistQuestionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setDetail( arg0.getDetail() );
        arg1.setResultId( arg0.getResultId() );
        arg1.setSpecialist( arg0.getSpecialist() );

        return arg1;
    }
}
