package org.dromara.facility.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.MonitorFacility;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MonitorFacilityVoToMonitorFacilityMapperImpl implements MonitorFacilityVoToMonitorFacilityMapper {

    @Override
    public MonitorFacility convert(MonitorFacilityVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MonitorFacility monitorFacility = new MonitorFacility();

        monitorFacility.setId( arg0.getId() );
        monitorFacility.setDevNo( arg0.getDevNo() );
        monitorFacility.setDeviceType( arg0.getDeviceType() );
        monitorFacility.setManufacturers( arg0.getManufacturers() );
        monitorFacility.setDataSources( arg0.getDataSources() );
        monitorFacility.setProjectId( arg0.getProjectId() );
        monitorFacility.setItemId( arg0.getItemId() );
        monitorFacility.setRemark( arg0.getRemark() );

        return monitorFacility;
    }

    @Override
    public MonitorFacility convert(MonitorFacilityVo arg0, MonitorFacility arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setManufacturers( arg0.getManufacturers() );
        arg1.setDataSources( arg0.getDataSources() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
