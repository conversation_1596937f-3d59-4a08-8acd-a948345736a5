package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.JlTowerRealVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class JlTowerRealToJlTowerRealVoMapperImpl implements JlTowerRealToJlTowerRealVoMapper {

    @Override
    public JlTowerRealVo convert(JlTowerReal arg0) {
        if ( arg0 == null ) {
            return null;
        }

        JlTowerRealVo jlTowerRealVo = new JlTowerRealVo();

        jlTowerRealVo.setId( arg0.getId() );
        jlTowerRealVo.setDevNo( arg0.getDevNo() );
        jlTowerRealVo.setTcNo( arg0.getTcNo() );
        jlTowerRealVo.setAddTime( arg0.getAddTime() );
        jlTowerRealVo.setLockMachineStatus( arg0.getLockMachineStatus() );
        jlTowerRealVo.setHeight( arg0.getHeight() );
        jlTowerRealVo.setRealRange( arg0.getRealRange() );
        jlTowerRealVo.setRotary( arg0.getRotary() );
        jlTowerRealVo.setStartWeight( arg0.getStartWeight() );
        jlTowerRealVo.setWindSpeed( arg0.getWindSpeed() );
        jlTowerRealVo.setSlant( arg0.getSlant() );
        jlTowerRealVo.setWeightPct( arg0.getWeightPct() );
        jlTowerRealVo.setMofPct( arg0.getMofPct() );
        jlTowerRealVo.setWindSpeedPct( arg0.getWindSpeedPct() );
        jlTowerRealVo.setSlantPct( arg0.getSlantPct() );
        jlTowerRealVo.setAlarmCause( arg0.getAlarmCause() );
        jlTowerRealVo.setAlarmCauseZh( arg0.getAlarmCauseZh() );
        jlTowerRealVo.setBrakingStatusUp( arg0.getBrakingStatusUp() );
        jlTowerRealVo.setBrakingStatusDown( arg0.getBrakingStatusDown() );
        jlTowerRealVo.setBrakingStatusFront( arg0.getBrakingStatusFront() );
        jlTowerRealVo.setBrakingStatusBack( arg0.getBrakingStatusBack() );
        jlTowerRealVo.setBrakingStatusLeft( arg0.getBrakingStatusLeft() );
        jlTowerRealVo.setBrakingStatusRight( arg0.getBrakingStatusRight() );
        jlTowerRealVo.setWorkingStatus( arg0.getWorkingStatus() );

        return jlTowerRealVo;
    }

    @Override
    public JlTowerRealVo convert(JlTowerReal arg0, JlTowerRealVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setTcNo( arg0.getTcNo() );
        arg1.setAddTime( arg0.getAddTime() );
        arg1.setLockMachineStatus( arg0.getLockMachineStatus() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setRealRange( arg0.getRealRange() );
        arg1.setRotary( arg0.getRotary() );
        arg1.setStartWeight( arg0.getStartWeight() );
        arg1.setWindSpeed( arg0.getWindSpeed() );
        arg1.setSlant( arg0.getSlant() );
        arg1.setWeightPct( arg0.getWeightPct() );
        arg1.setMofPct( arg0.getMofPct() );
        arg1.setWindSpeedPct( arg0.getWindSpeedPct() );
        arg1.setSlantPct( arg0.getSlantPct() );
        arg1.setAlarmCause( arg0.getAlarmCause() );
        arg1.setAlarmCauseZh( arg0.getAlarmCauseZh() );
        arg1.setBrakingStatusUp( arg0.getBrakingStatusUp() );
        arg1.setBrakingStatusDown( arg0.getBrakingStatusDown() );
        arg1.setBrakingStatusFront( arg0.getBrakingStatusFront() );
        arg1.setBrakingStatusBack( arg0.getBrakingStatusBack() );
        arg1.setBrakingStatusLeft( arg0.getBrakingStatusLeft() );
        arg1.setBrakingStatusRight( arg0.getBrakingStatusRight() );
        arg1.setWorkingStatus( arg0.getWorkingStatus() );

        return arg1;
    }
}
