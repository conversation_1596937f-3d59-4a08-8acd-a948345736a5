package org.dromara.projects.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.bo.PrjHazardousItemsBoToPrjHazardousItemsMapper;
import org.dromara.projects.domain.vo.PrjHazardousItemsVo;
import org.dromara.projects.domain.vo.PrjHazardousItemsVoToPrjHazardousItemsMapper;
import org.dromara.projects.domain.vo.PrjProjectsVoToPrjProjectsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {PrjProjectsVoToPrjProjectsMapper.class,PrjProjectsToPrjProjectsVoMapper.class,PrjHazardousItemsVoToPrjHazardousItemsMapper.class,PrjHazardousItemsBoToPrjHazardousItemsMapper.class},
    imports = {}
)
public interface PrjHazardousItemsToPrjHazardousItemsVoMapper extends BaseMapper<PrjHazardousItems, PrjHazardousItemsVo> {
}
