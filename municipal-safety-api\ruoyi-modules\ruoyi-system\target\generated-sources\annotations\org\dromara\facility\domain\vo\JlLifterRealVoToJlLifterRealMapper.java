package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.JlLifterReal;
import org.dromara.facility.domain.JlLifterRealToJlLifterRealVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {JlLifterRealToJlLifterRealVoMapper.class},
    imports = {}
)
public interface JlLifterRealVoToJlLifterRealMapper extends BaseMapper<JlLifterRealVo, JlLifterReal> {
}
