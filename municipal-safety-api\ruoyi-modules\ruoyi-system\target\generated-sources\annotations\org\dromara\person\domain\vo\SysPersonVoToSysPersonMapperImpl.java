package org.dromara.person.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.person.domain.SysPerson;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SysPersonVoToSysPersonMapperImpl implements SysPersonVoToSysPersonMapper {

    @Override
    public SysPerson convert(SysPersonVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysPerson sysPerson = new SysPerson();

        sysPerson.setPersonId( arg0.getPersonId() );
        sysPerson.setEnterpriseId( arg0.getEnterpriseId() );
        sysPerson.setUserId( arg0.getUserId() );
        sysPerson.setName( arg0.getName() );
        sysPerson.setIdCard( arg0.getIdCard() );
        sysPerson.setPhone( arg0.getPhone() );
        sysPerson.setNativePlace( arg0.getNativePlace() );
        sysPerson.setGender( arg0.getGender() );
        sysPerson.setPoliticalStatus( arg0.getPoliticalStatus() );
        sysPerson.setEducation( arg0.getEducation() );
        sysPerson.setHeadImgId( arg0.getHeadImgId() );

        return sysPerson;
    }

    @Override
    public SysPerson convert(SysPersonVo arg0, SysPerson arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setPersonId( arg0.getPersonId() );
        arg1.setEnterpriseId( arg0.getEnterpriseId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setName( arg0.getName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setNativePlace( arg0.getNativePlace() );
        arg1.setGender( arg0.getGender() );
        arg1.setPoliticalStatus( arg0.getPoliticalStatus() );
        arg1.setEducation( arg0.getEducation() );
        arg1.setHeadImgId( arg0.getHeadImgId() );

        return arg1;
    }
}
