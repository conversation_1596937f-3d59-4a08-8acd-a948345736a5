package org.dromara.quality.domain.bo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.quality.domain.QualityMeasurement;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {},
    imports = {}
)
public interface QualityMeasurementBoToQualityMeasurementMapper extends BaseMapper<QualityMeasurementBo, QualityMeasurement> {
}
