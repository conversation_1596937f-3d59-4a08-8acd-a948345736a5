package org.dromara.projects.domain;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.vo.PrjHazardousItemsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsToPrjHazardousItemsVoMapperImpl implements PrjHazardousItemsToPrjHazardousItemsVoMapper {

    @Override
    public PrjHazardousItemsVo convert(PrjHazardousItems arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsVo prjHazardousItemsVo = new PrjHazardousItemsVo();

        prjHazardousItemsVo.setItemId( arg0.getItemId() );
        prjHazardousItemsVo.setProjectId( arg0.getProjectId() );
        prjHazardousItemsVo.setDangerId( arg0.getDangerId() );
        prjHazardousItemsVo.setItemName( arg0.getItemName() );
        prjHazardousItemsVo.setScopeDetails( arg0.getScopeDetails() );
        prjHazardousItemsVo.setDangerListType( arg0.getDangerListType() );
        prjHazardousItemsVo.setStartDate( arg0.getStartDate() );
        prjHazardousItemsVo.setPlannedEndDate( arg0.getPlannedEndDate() );
        prjHazardousItemsVo.setActualEndDate( arg0.getActualEndDate() );
        prjHazardousItemsVo.setActualStartDate( arg0.getActualStartDate() );
        prjHazardousItemsVo.setStatus( arg0.getStatus() );

        return prjHazardousItemsVo;
    }

    @Override
    public PrjHazardousItemsVo convert(PrjHazardousItems arg0, PrjHazardousItemsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setItemId( arg0.getItemId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setDangerId( arg0.getDangerId() );
        arg1.setItemName( arg0.getItemName() );
        arg1.setScopeDetails( arg0.getScopeDetails() );
        arg1.setDangerListType( arg0.getDangerListType() );
        arg1.setStartDate( arg0.getStartDate() );
        arg1.setPlannedEndDate( arg0.getPlannedEndDate() );
        arg1.setActualEndDate( arg0.getActualEndDate() );
        arg1.setActualStartDate( arg0.getActualStartDate() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
