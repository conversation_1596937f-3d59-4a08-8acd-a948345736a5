package org.dromara.projects.domain;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.vo.PrjConstructionPlansVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjConstructionPlansToPrjConstructionPlansVoMapperImpl implements PrjConstructionPlansToPrjConstructionPlansVoMapper {

    @Override
    public PrjConstructionPlansVo convert(PrjConstructionPlans arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjConstructionPlansVo prjConstructionPlansVo = new PrjConstructionPlansVo();

        prjConstructionPlansVo.setPlanId( arg0.getPlanId() );
        prjConstructionPlansVo.setItemId( arg0.getItemId() );
        prjConstructionPlansVo.setPlanName( arg0.getPlanName() );
        prjConstructionPlansVo.setPlanVersion( arg0.getPlanVersion() );
        prjConstructionPlansVo.setPlanDocumentId( arg0.getPlanDocumentId() );
        prjConstructionPlansVo.setApprovalFormDocId( arg0.getApprovalFormDocId() );
        prjConstructionPlansVo.setReviewStatus( arg0.getReviewStatus() );
        prjConstructionPlansVo.setAiDefectWarningDetails( arg0.getAiDefectWarningDetails() );

        return prjConstructionPlansVo;
    }

    @Override
    public PrjConstructionPlansVo convert(PrjConstructionPlans arg0, PrjConstructionPlansVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setPlanId( arg0.getPlanId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setPlanName( arg0.getPlanName() );
        arg1.setPlanVersion( arg0.getPlanVersion() );
        arg1.setPlanDocumentId( arg0.getPlanDocumentId() );
        arg1.setApprovalFormDocId( arg0.getApprovalFormDocId() );
        arg1.setReviewStatus( arg0.getReviewStatus() );
        arg1.setAiDefectWarningDetails( arg0.getAiDefectWarningDetails() );

        return arg1;
    }
}
