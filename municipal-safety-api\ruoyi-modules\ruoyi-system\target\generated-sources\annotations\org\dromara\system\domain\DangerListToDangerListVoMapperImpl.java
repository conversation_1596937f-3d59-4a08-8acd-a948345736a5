package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.DangerListVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class DangerListToDangerListVoMapperImpl implements DangerListToDangerListVoMapper {

    @Override
    public DangerListVo convert(DangerList arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DangerListVo dangerListVo = new DangerListVo();

        dangerListVo.setDangerId( arg0.getDangerId() );
        dangerListVo.setName( arg0.getName() );
        dangerListVo.setPreId( arg0.getPreId() );
        dangerListVo.setType( arg0.getType() );
        dangerListVo.setRemark( arg0.getRemark() );

        return dangerListVo;
    }

    @Override
    public DangerListVo convert(DangerList arg0, DangerListVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setDangerId( arg0.getDangerId() );
        arg1.setName( arg0.getName() );
        arg1.setPreId( arg0.getPreId() );
        arg1.setType( arg0.getType() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
