package org.dromara.plan.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.expert.domain.ExpertToExpertVoMapper;
import org.dromara.expert.domain.vo.ExpertVoToExpertMapper;
import org.dromara.plan.domain.bo.PrjPatrolPlanBoToPrjPatrolPlanMapper;
import org.dromara.plan.domain.vo.PrjPatrolPlanVo;
import org.dromara.plan.domain.vo.PrjPatrolPlanVoToPrjPatrolPlanMapper;
import org.dromara.projects.domain.PrjProjectsToPrjProjectsVoMapper;
import org.dromara.projects.domain.vo.PrjProjectsVoToPrjProjectsMapper;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {SysDeptVoToSysDeptMapper.class,SysDeptToSysDeptVoMapper.class,ExpertVoToExpertMapper.class,ExpertToExpertVoMapper.class,PrjProjectsVoToPrjProjectsMapper.class,PrjProjectsToPrjProjectsVoMapper.class,PrjPatrolPlanBoToPrjPatrolPlanMapper.class,PrjPatrolPlanVoToPrjPatrolPlanMapper.class},
    imports = {}
)
public interface PrjPatrolPlanToPrjPatrolPlanVoMapper extends BaseMapper<PrjPatrolPlan, PrjPatrolPlanVo> {
}
