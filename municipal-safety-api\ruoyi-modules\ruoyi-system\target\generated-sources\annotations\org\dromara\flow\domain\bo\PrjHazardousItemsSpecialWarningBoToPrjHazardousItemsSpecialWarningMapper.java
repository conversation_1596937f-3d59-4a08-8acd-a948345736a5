package org.dromara.flow.domain.bo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.PrjHazardousItemsSpecialWarning;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {},
    imports = {}
)
public interface PrjHazardousItemsSpecialWarningBoToPrjHazardousItemsSpecialWarningMapper extends BaseMapper<PrjHazardousItemsSpecialWarningBo, PrjHazardousItemsSpecialWarning> {
}
