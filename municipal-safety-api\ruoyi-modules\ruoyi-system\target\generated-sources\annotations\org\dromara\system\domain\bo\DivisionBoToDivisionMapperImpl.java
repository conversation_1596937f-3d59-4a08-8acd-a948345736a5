package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.Division;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class DivisionBoToDivisionMapperImpl implements DivisionBoToDivisionMapper {

    @Override
    public Division convert(DivisionBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Division division = new Division();

        division.setSearchValue( arg0.getSearchValue() );
        division.setCreateDept( arg0.getCreateDept() );
        division.setCreateBy( arg0.getCreateBy() );
        division.setCreateTime( arg0.getCreateTime() );
        division.setUpdateBy( arg0.getUpdateBy() );
        division.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            division.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        division.setDivisionId( arg0.getDivisionId() );
        division.setDivisionCode( arg0.getDivisionCode() );
        division.setDivisionName( arg0.getDivisionName() );
        division.setLevel( arg0.getLevel() );
        division.setParentCode( arg0.getParentCode() );
        division.setDelFlag( arg0.getDelFlag() );

        return division;
    }

    @Override
    public Division convert(DivisionBo arg0, Division arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setDivisionId( arg0.getDivisionId() );
        arg1.setDivisionCode( arg0.getDivisionCode() );
        arg1.setDivisionName( arg0.getDivisionName() );
        arg1.setLevel( arg0.getLevel() );
        arg1.setParentCode( arg0.getParentCode() );
        arg1.setDelFlag( arg0.getDelFlag() );

        return arg1;
    }
}
