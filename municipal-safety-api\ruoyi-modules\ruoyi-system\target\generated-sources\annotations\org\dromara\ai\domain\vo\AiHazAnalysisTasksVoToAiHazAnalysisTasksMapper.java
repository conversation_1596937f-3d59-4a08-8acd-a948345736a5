package org.dromara.ai.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.dromara.ai.domain.AiHazAnalysisTasksToAiHazAnalysisTasksVoMapper;
import org.dromara.projects.domain.PrjHazardousItemsToPrjHazardousItemsVoMapper;
import org.dromara.projects.domain.PrjProjectsToPrjProjectsVoMapper;
import org.dromara.projects.domain.vo.PrjHazardousItemsVoToPrjHazardousItemsMapper;
import org.dromara.projects.domain.vo.PrjProjectsVoToPrjProjectsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {PrjProjectsVoToPrjProjectsMapper.class,PrjProjectsToPrjProjectsVoMapper.class,PrjHazardousItemsVoToPrjHazardousItemsMapper.class,PrjHazardousItemsToPrjHazardousItemsVoMapper.class,AiHazAnalysisTasksToAiHazAnalysisTasksVoMapper.class},
    imports = {}
)
public interface AiHazAnalysisTasksVoToAiHazAnalysisTasksMapper extends BaseMapper<AiHazAnalysisTasksVo, AiHazAnalysisTasks> {
}
