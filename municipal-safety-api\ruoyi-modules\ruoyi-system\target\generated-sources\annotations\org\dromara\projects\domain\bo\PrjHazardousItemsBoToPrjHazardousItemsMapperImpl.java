package org.dromara.projects.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjHazardousItems;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsBoToPrjHazardousItemsMapperImpl implements PrjHazardousItemsBoToPrjHazardousItemsMapper {

    @Override
    public PrjHazardousItems convert(PrjHazardousItemsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItems prjHazardousItems = new PrjHazardousItems();

        prjHazardousItems.setSearchValue( arg0.getSearchValue() );
        prjHazardousItems.setCreateDept( arg0.getCreateDept() );
        prjHazardousItems.setCreateBy( arg0.getCreateBy() );
        prjHazardousItems.setCreateTime( arg0.getCreateTime() );
        prjHazardousItems.setUpdateBy( arg0.getUpdateBy() );
        prjHazardousItems.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjHazardousItems.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjHazardousItems.setItemId( arg0.getItemId() );
        prjHazardousItems.setProjectId( arg0.getProjectId() );
        prjHazardousItems.setDangerId( arg0.getDangerId() );
        prjHazardousItems.setItemName( arg0.getItemName() );
        prjHazardousItems.setScopeDetails( arg0.getScopeDetails() );
        prjHazardousItems.setDangerListType( arg0.getDangerListType() );
        prjHazardousItems.setStartDate( arg0.getStartDate() );
        prjHazardousItems.setPlannedEndDate( arg0.getPlannedEndDate() );
        prjHazardousItems.setActualEndDate( arg0.getActualEndDate() );
        prjHazardousItems.setActualStartDate( arg0.getActualStartDate() );
        prjHazardousItems.setStatus( arg0.getStatus() );

        return prjHazardousItems;
    }

    @Override
    public PrjHazardousItems convert(PrjHazardousItemsBo arg0, PrjHazardousItems arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setItemId( arg0.getItemId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setDangerId( arg0.getDangerId() );
        arg1.setItemName( arg0.getItemName() );
        arg1.setScopeDetails( arg0.getScopeDetails() );
        arg1.setDangerListType( arg0.getDangerListType() );
        arg1.setStartDate( arg0.getStartDate() );
        arg1.setPlannedEndDate( arg0.getPlannedEndDate() );
        arg1.setActualEndDate( arg0.getActualEndDate() );
        arg1.setActualStartDate( arg0.getActualStartDate() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
