package org.dromara.attendance.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.MAttPerson;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttPersonBoToMAttPersonMapperImpl implements MAttPersonBoToMAttPersonMapper {

    @Override
    public MAttPerson convert(MAttPersonBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttPerson mAttPerson = new MAttPerson();

        mAttPerson.setSearchValue( arg0.getSearchValue() );
        mAttPerson.setCreateDept( arg0.getCreateDept() );
        mAttPerson.setCreateBy( arg0.getCreateBy() );
        mAttPerson.setCreateTime( arg0.getCreateTime() );
        mAttPerson.setUpdateBy( arg0.getUpdateBy() );
        mAttPerson.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            mAttPerson.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        mAttPerson.setId( arg0.getId() );
        mAttPerson.setPersonId( arg0.getPersonId() );
        mAttPerson.setSnId( arg0.getSnId() );

        return mAttPerson;
    }

    @Override
    public MAttPerson convert(MAttPersonBo arg0, MAttPerson arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setSnId( arg0.getSnId() );

        return arg1;
    }
}
