package org.dromara.quality.domain;

import javax.annotation.processing.Generated;
import org.dromara.quality.domain.vo.QualityMeasurementVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class QualityMeasurementToQualityMeasurementVoMapperImpl implements QualityMeasurementToQualityMeasurementVoMapper {

    @Override
    public QualityMeasurementVo convert(QualityMeasurement arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QualityMeasurementVo qualityMeasurementVo = new QualityMeasurementVo();

        qualityMeasurementVo.setMeasurementId( arg0.getMeasurementId() );
        qualityMeasurementVo.setMeasurementTime( arg0.getMeasurementTime() );
        qualityMeasurementVo.setMeasurementItem( arg0.getMeasurementItem() );
        qualityMeasurementVo.setDeviceId( arg0.getDeviceId() );
        qualityMeasurementVo.setDeviceName( arg0.getDeviceName() );
        qualityMeasurementVo.setDeviceCode( arg0.getDeviceCode() );
        qualityMeasurementVo.setMeasurementResult( arg0.getMeasurementResult() );
        qualityMeasurementVo.setIsCompliant( arg0.getIsCompliant() );
        qualityMeasurementVo.setProjectName( arg0.getProjectName() );
        qualityMeasurementVo.setMeasurementLocation( arg0.getMeasurementLocation() );
        qualityMeasurementVo.setStandardValue( arg0.getStandardValue() );
        qualityMeasurementVo.setDeviationValue( arg0.getDeviationValue() );
        qualityMeasurementVo.setMeasurementPerson( arg0.getMeasurementPerson() );
        qualityMeasurementVo.setIsHazardMarked( arg0.getIsHazardMarked() );
        qualityMeasurementVo.setHazardDescription( arg0.getHazardDescription() );
        qualityMeasurementVo.setStatus( arg0.getStatus() );
        qualityMeasurementVo.setRemark( arg0.getRemark() );
        qualityMeasurementVo.setCreateTime( arg0.getCreateTime() );
        qualityMeasurementVo.setCreateBy( arg0.getCreateBy() );
        qualityMeasurementVo.setUpdateTime( arg0.getUpdateTime() );
        qualityMeasurementVo.setUpdateBy( arg0.getUpdateBy() );

        return qualityMeasurementVo;
    }

    @Override
    public QualityMeasurementVo convert(QualityMeasurement arg0, QualityMeasurementVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setMeasurementId( arg0.getMeasurementId() );
        arg1.setMeasurementTime( arg0.getMeasurementTime() );
        arg1.setMeasurementItem( arg0.getMeasurementItem() );
        arg1.setDeviceId( arg0.getDeviceId() );
        arg1.setDeviceName( arg0.getDeviceName() );
        arg1.setDeviceCode( arg0.getDeviceCode() );
        arg1.setMeasurementResult( arg0.getMeasurementResult() );
        arg1.setIsCompliant( arg0.getIsCompliant() );
        arg1.setProjectName( arg0.getProjectName() );
        arg1.setMeasurementLocation( arg0.getMeasurementLocation() );
        arg1.setStandardValue( arg0.getStandardValue() );
        arg1.setDeviationValue( arg0.getDeviationValue() );
        arg1.setMeasurementPerson( arg0.getMeasurementPerson() );
        arg1.setIsHazardMarked( arg0.getIsHazardMarked() );
        arg1.setHazardDescription( arg0.getHazardDescription() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );

        return arg1;
    }
}
