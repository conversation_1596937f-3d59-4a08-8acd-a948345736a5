package org.dromara.special.domain;

import javax.annotation.processing.Generated;
import org.dromara.special.domain.vo.SpecialEquipmentVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SpecialEquipmentToSpecialEquipmentVoMapperImpl implements SpecialEquipmentToSpecialEquipmentVoMapper {

    @Override
    public SpecialEquipmentVo convert(SpecialEquipment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SpecialEquipmentVo specialEquipmentVo = new SpecialEquipmentVo();

        specialEquipmentVo.setEquipmentId( arg0.getEquipmentId() );
        specialEquipmentVo.setCertificateNumber( arg0.getCertificateNumber() );
        specialEquipmentVo.setIssuer( arg0.getIssuer() );
        specialEquipmentVo.setIssueDate( arg0.getIssueDate() );
        specialEquipmentVo.setUseRegistrationCertificate( arg0.getUseRegistrationCertificate() );
        specialEquipmentVo.setEquipmentCategory( arg0.getEquipmentCategory() );
        specialEquipmentVo.setModelSpec( arg0.getModelSpec() );
        specialEquipmentVo.setFactoryNumber( arg0.getFactoryNumber() );
        specialEquipmentVo.setRecordNumber( arg0.getRecordNumber() );
        specialEquipmentVo.setManufacturer( arg0.getManufacturer() );
        specialEquipmentVo.setPropertyOwner( arg0.getPropertyOwner() );
        specialEquipmentVo.setProjectName( arg0.getProjectName() );
        specialEquipmentVo.setProjectAddress( arg0.getProjectAddress() );
        specialEquipmentVo.setProjectId( arg0.getProjectId() );
        specialEquipmentVo.setItemId( arg0.getItemId() );
        specialEquipmentVo.setUsageUnit( arg0.getUsageUnit() );
        specialEquipmentVo.setMaintenanceUnit( arg0.getMaintenanceUnit() );
        specialEquipmentVo.setInstallationUnit( arg0.getInstallationUnit() );
        specialEquipmentVo.setInspectionUnit( arg0.getInspectionUnit() );
        specialEquipmentVo.setProjectManager( arg0.getProjectManager() );
        specialEquipmentVo.setInstallationDate( arg0.getInstallationDate() );
        specialEquipmentVo.setInspectionDate( arg0.getInspectionDate() );
        specialEquipmentVo.setEnterDate( arg0.getEnterDate() );
        specialEquipmentVo.setExitDate( arg0.getExitDate() );
        specialEquipmentVo.setLocation( arg0.getLocation() );
        specialEquipmentVo.setSopId( arg0.getSopId() );
        specialEquipmentVo.setRemarks( arg0.getRemarks() );
        specialEquipmentVo.setDevNo( arg0.getDevNo() );

        return specialEquipmentVo;
    }

    @Override
    public SpecialEquipmentVo convert(SpecialEquipment arg0, SpecialEquipmentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setEquipmentId( arg0.getEquipmentId() );
        arg1.setCertificateNumber( arg0.getCertificateNumber() );
        arg1.setIssuer( arg0.getIssuer() );
        arg1.setIssueDate( arg0.getIssueDate() );
        arg1.setUseRegistrationCertificate( arg0.getUseRegistrationCertificate() );
        arg1.setEquipmentCategory( arg0.getEquipmentCategory() );
        arg1.setModelSpec( arg0.getModelSpec() );
        arg1.setFactoryNumber( arg0.getFactoryNumber() );
        arg1.setRecordNumber( arg0.getRecordNumber() );
        arg1.setManufacturer( arg0.getManufacturer() );
        arg1.setPropertyOwner( arg0.getPropertyOwner() );
        arg1.setProjectName( arg0.getProjectName() );
        arg1.setProjectAddress( arg0.getProjectAddress() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setUsageUnit( arg0.getUsageUnit() );
        arg1.setMaintenanceUnit( arg0.getMaintenanceUnit() );
        arg1.setInstallationUnit( arg0.getInstallationUnit() );
        arg1.setInspectionUnit( arg0.getInspectionUnit() );
        arg1.setProjectManager( arg0.getProjectManager() );
        arg1.setInstallationDate( arg0.getInstallationDate() );
        arg1.setInspectionDate( arg0.getInspectionDate() );
        arg1.setEnterDate( arg0.getEnterDate() );
        arg1.setExitDate( arg0.getExitDate() );
        arg1.setLocation( arg0.getLocation() );
        arg1.setSopId( arg0.getSopId() );
        arg1.setRemarks( arg0.getRemarks() );
        arg1.setDevNo( arg0.getDevNo() );

        return arg1;
    }
}
