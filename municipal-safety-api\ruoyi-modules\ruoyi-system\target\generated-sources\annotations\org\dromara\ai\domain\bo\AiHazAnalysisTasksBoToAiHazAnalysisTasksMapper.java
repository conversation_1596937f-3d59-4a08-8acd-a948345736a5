package org.dromara.ai.domain.bo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {},
    imports = {}
)
public interface AiHazAnalysisTasksBoToAiHazAnalysisTasksMapper extends BaseMapper<AiHazAnalysisTasksBo, AiHazAnalysisTasks> {
}
