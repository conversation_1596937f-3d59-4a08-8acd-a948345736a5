package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.LnHighFormworkRealVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnHighFormworkRealToLnHighFormworkRealVoMapperImpl implements LnHighFormworkRealToLnHighFormworkRealVoMapper {

    @Override
    public LnHighFormworkRealVo convert(LnHighFormworkReal arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnHighFormworkRealVo lnHighFormworkRealVo = new LnHighFormworkRealVo();

        lnHighFormworkRealVo.setId( arg0.getId() );
        lnHighFormworkRealVo.setEid( arg0.getEid() );
        lnHighFormworkRealVo.setSettlement( arg0.getSettlement() );
        lnHighFormworkRealVo.setInclinationAngleOfVerticalPole( arg0.getInclinationAngleOfVerticalPole() );
        lnHighFormworkRealVo.setHorizontalInclination( arg0.getHorizontalInclination() );
        lnHighFormworkRealVo.setBearing( arg0.getBearing() );
        lnHighFormworkRealVo.setHorizontalDisplacement( arg0.getHorizontalDisplacement() );
        lnHighFormworkRealVo.setVerticalDisplacement( arg0.getVerticalDisplacement() );
        lnHighFormworkRealVo.setEventTime( arg0.getEventTime() );
        lnHighFormworkRealVo.setDevNo( arg0.getDevNo() );
        lnHighFormworkRealVo.setCreateTime( arg0.getCreateTime() );

        return lnHighFormworkRealVo;
    }

    @Override
    public LnHighFormworkRealVo convert(LnHighFormworkReal arg0, LnHighFormworkRealVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setEid( arg0.getEid() );
        arg1.setSettlement( arg0.getSettlement() );
        arg1.setInclinationAngleOfVerticalPole( arg0.getInclinationAngleOfVerticalPole() );
        arg1.setHorizontalInclination( arg0.getHorizontalInclination() );
        arg1.setBearing( arg0.getBearing() );
        arg1.setHorizontalDisplacement( arg0.getHorizontalDisplacement() );
        arg1.setVerticalDisplacement( arg0.getVerticalDisplacement() );
        arg1.setEventTime( arg0.getEventTime() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
