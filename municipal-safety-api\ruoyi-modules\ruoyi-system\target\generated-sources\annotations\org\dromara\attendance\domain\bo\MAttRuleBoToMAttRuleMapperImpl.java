package org.dromara.attendance.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.MAttRule;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttRuleBoToMAttRuleMapperImpl implements MAttRuleBoToMAttRuleMapper {

    @Override
    public MAttRule convert(MAttRuleBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttRule mAttRule = new MAttRule();

        mAttRule.setSearchValue( arg0.getSearchValue() );
        mAttRule.setCreateDept( arg0.getCreateDept() );
        mAttRule.setCreateBy( arg0.getCreateBy() );
        mAttRule.setCreateTime( arg0.getCreateTime() );
        mAttRule.setUpdateBy( arg0.getUpdateBy() );
        mAttRule.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            mAttRule.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        mAttRule.setId( arg0.getId() );
        mAttRule.setRuleType( arg0.getRuleType() );
        mAttRule.setProjectId( arg0.getProjectId() );
        mAttRule.setPersonType( arg0.getPersonType() );
        mAttRule.setIsAll( arg0.getIsAll() );
        mAttRule.setCheckTime( arg0.getCheckTime() );
        mAttRule.setElasticTime( arg0.getElasticTime() );
        mAttRule.setWarning( arg0.getWarning() );
        mAttRule.setFieldCheck( arg0.getFieldCheck() );
        mAttRule.setContent( arg0.getContent() );

        return mAttRule;
    }

    @Override
    public MAttRule convert(MAttRuleBo arg0, MAttRule arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setRuleType( arg0.getRuleType() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setPersonType( arg0.getPersonType() );
        arg1.setIsAll( arg0.getIsAll() );
        arg1.setCheckTime( arg0.getCheckTime() );
        arg1.setElasticTime( arg0.getElasticTime() );
        arg1.setWarning( arg0.getWarning() );
        arg1.setFieldCheck( arg0.getFieldCheck() );
        arg1.setContent( arg0.getContent() );

        return arg1;
    }
}
