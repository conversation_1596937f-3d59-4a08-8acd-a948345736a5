package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.MonitorFacilityVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MonitorFacilityToMonitorFacilityVoMapperImpl implements MonitorFacilityToMonitorFacilityVoMapper {

    @Override
    public MonitorFacilityVo convert(MonitorFacility arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MonitorFacilityVo monitorFacilityVo = new MonitorFacilityVo();

        monitorFacilityVo.setId( arg0.getId() );
        monitorFacilityVo.setDevNo( arg0.getDevNo() );
        monitorFacilityVo.setDeviceType( arg0.getDeviceType() );
        monitorFacilityVo.setManufacturers( arg0.getManufacturers() );
        monitorFacilityVo.setDataSources( arg0.getDataSources() );
        monitorFacilityVo.setProjectId( arg0.getProjectId() );
        monitorFacilityVo.setItemId( arg0.getItemId() );
        monitorFacilityVo.setRemark( arg0.getRemark() );

        return monitorFacilityVo;
    }

    @Override
    public MonitorFacilityVo convert(MonitorFacility arg0, MonitorFacilityVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setManufacturers( arg0.getManufacturers() );
        arg1.setDataSources( arg0.getDataSources() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
