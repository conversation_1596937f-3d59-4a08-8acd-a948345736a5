package org.dromara.person.domain;

import javax.annotation.processing.Generated;
import org.dromara.person.domain.vo.SysPersonVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SysPersonToSysPersonVoMapperImpl implements SysPersonToSysPersonVoMapper {

    @Override
    public SysPersonVo convert(SysPerson arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysPersonVo sysPersonVo = new SysPersonVo();

        sysPersonVo.setPersonId( arg0.getPersonId() );
        sysPersonVo.setEnterpriseId( arg0.getEnterpriseId() );
        sysPersonVo.setUserId( arg0.getUserId() );
        sysPersonVo.setName( arg0.getName() );
        sysPersonVo.setIdCard( arg0.getIdCard() );
        sysPersonVo.setPhone( arg0.getPhone() );
        sysPersonVo.setNativePlace( arg0.getNativePlace() );
        sysPersonVo.setGender( arg0.getGender() );
        sysPersonVo.setPoliticalStatus( arg0.getPoliticalStatus() );
        sysPersonVo.setEducation( arg0.getEducation() );
        sysPersonVo.setHeadImgId( arg0.getHeadImgId() );

        return sysPersonVo;
    }

    @Override
    public SysPersonVo convert(SysPerson arg0, SysPersonVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setPersonId( arg0.getPersonId() );
        arg1.setEnterpriseId( arg0.getEnterpriseId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setName( arg0.getName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setNativePlace( arg0.getNativePlace() );
        arg1.setGender( arg0.getGender() );
        arg1.setPoliticalStatus( arg0.getPoliticalStatus() );
        arg1.setEducation( arg0.getEducation() );
        arg1.setHeadImgId( arg0.getHeadImgId() );

        return arg1;
    }
}
