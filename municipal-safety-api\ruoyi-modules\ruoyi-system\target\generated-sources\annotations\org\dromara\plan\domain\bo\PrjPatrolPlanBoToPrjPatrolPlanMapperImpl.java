package org.dromara.plan.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.plan.domain.PrjPatrolPlan;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjPatrolPlanBoToPrjPatrolPlanMapperImpl implements PrjPatrolPlanBoToPrjPatrolPlanMapper {

    @Override
    public PrjPatrolPlan convert(PrjPatrolPlanBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjPatrolPlan prjPatrolPlan = new PrjPatrolPlan();

        prjPatrolPlan.setSearchValue( arg0.getSearchValue() );
        prjPatrolPlan.setCreateDept( arg0.getCreateDept() );
        prjPatrolPlan.setCreateBy( arg0.getCreateBy() );
        prjPatrolPlan.setCreateTime( arg0.getCreateTime() );
        prjPatrolPlan.setUpdateBy( arg0.getUpdateBy() );
        prjPatrolPlan.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjPatrolPlan.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjPatrolPlan.setPlanId( arg0.getPlanId() );
        prjPatrolPlan.setPlanName( arg0.getPlanName() );
        prjPatrolPlan.setBeginTime( arg0.getBeginTime() );
        prjPatrolPlan.setEndTime( arg0.getEndTime() );
        prjPatrolPlan.setDeptIds( arg0.getDeptIds() );
        prjPatrolPlan.setProjectIds( arg0.getProjectIds() );
        prjPatrolPlan.setExpertIds( arg0.getExpertIds() );
        prjPatrolPlan.setRemarks( arg0.getRemarks() );

        return prjPatrolPlan;
    }

    @Override
    public PrjPatrolPlan convert(PrjPatrolPlanBo arg0, PrjPatrolPlan arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setPlanId( arg0.getPlanId() );
        arg1.setPlanName( arg0.getPlanName() );
        arg1.setBeginTime( arg0.getBeginTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setDeptIds( arg0.getDeptIds() );
        arg1.setProjectIds( arg0.getProjectIds() );
        arg1.setExpertIds( arg0.getExpertIds() );
        arg1.setRemarks( arg0.getRemarks() );

        return arg1;
    }
}
