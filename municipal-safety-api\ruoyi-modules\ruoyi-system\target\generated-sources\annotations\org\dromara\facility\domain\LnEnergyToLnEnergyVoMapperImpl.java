package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.LnEnergyVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnEnergyToLnEnergyVoMapperImpl implements LnEnergyToLnEnergyVoMapper {

    @Override
    public LnEnergyVo convert(LnEnergy arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnEnergyVo lnEnergyVo = new LnEnergyVo();

        lnEnergyVo.setId( arg0.getId() );
        lnEnergyVo.setUa( arg0.getUa() );
        lnEnergyVo.setUb( arg0.getUb() );
        lnEnergyVo.setUc( arg0.getUc() );
        lnEnergyVo.setIa( arg0.getIa() );
        lnEnergyVo.setIb( arg0.getIb() );
        lnEnergyVo.setIc( arg0.getIc() );
        lnEnergyVo.setIl( arg0.getIl() );
        lnEnergyVo.setTa( arg0.getTa() );
        lnEnergyVo.setTb( arg0.getTb() );
        lnEnergyVo.setTc( arg0.getTc() );
        lnEnergyVo.setTn( arg0.getTn() );
        lnEnergyVo.setRecordTime( arg0.getRecordTime() );
        lnEnergyVo.setDeviceNo( arg0.getDeviceNo() );
        lnEnergyVo.setDevNo( arg0.getDevNo() );
        lnEnergyVo.setCreateTime( arg0.getCreateTime() );

        return lnEnergyVo;
    }

    @Override
    public LnEnergyVo convert(LnEnergy arg0, LnEnergyVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUa( arg0.getUa() );
        arg1.setUb( arg0.getUb() );
        arg1.setUc( arg0.getUc() );
        arg1.setIa( arg0.getIa() );
        arg1.setIb( arg0.getIb() );
        arg1.setIc( arg0.getIc() );
        arg1.setIl( arg0.getIl() );
        arg1.setTa( arg0.getTa() );
        arg1.setTb( arg0.getTb() );
        arg1.setTc( arg0.getTc() );
        arg1.setTn( arg0.getTn() );
        arg1.setRecordTime( arg0.getRecordTime() );
        arg1.setDeviceNo( arg0.getDeviceNo() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
