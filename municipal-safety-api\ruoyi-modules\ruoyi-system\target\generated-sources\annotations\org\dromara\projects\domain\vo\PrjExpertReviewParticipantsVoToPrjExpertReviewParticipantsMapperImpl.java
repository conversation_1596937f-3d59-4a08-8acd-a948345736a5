package org.dromara.projects.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjExpertReviewParticipants;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjExpertReviewParticipantsVoToPrjExpertReviewParticipantsMapperImpl implements PrjExpertReviewParticipantsVoToPrjExpertReviewParticipantsMapper {

    @Override
    public PrjExpertReviewParticipants convert(PrjExpertReviewParticipantsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjExpertReviewParticipants prjExpertReviewParticipants = new PrjExpertReviewParticipants();

        prjExpertReviewParticipants.setReviewId( arg0.getReviewId() );
        prjExpertReviewParticipants.setUserId( arg0.getUserId() );
        prjExpertReviewParticipants.setRoleInMeeting( arg0.getRoleInMeeting() );
        prjExpertReviewParticipants.setIsAttendingExpert( arg0.getIsAttendingExpert() );

        return prjExpertReviewParticipants;
    }

    @Override
    public PrjExpertReviewParticipants convert(PrjExpertReviewParticipantsVo arg0, PrjExpertReviewParticipants arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setReviewId( arg0.getReviewId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setRoleInMeeting( arg0.getRoleInMeeting() );
        arg1.setIsAttendingExpert( arg0.getIsAttendingExpert() );

        return arg1;
    }
}
