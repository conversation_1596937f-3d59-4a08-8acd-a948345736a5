package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.LnSmokeVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnSmokeToLnSmokeVoMapperImpl implements LnSmokeToLnSmokeVoMapper {

    @Override
    public LnSmokeVo convert(LnSmoke arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnSmokeVo lnSmokeVo = new LnSmokeVo();

        lnSmokeVo.setId( arg0.getId() );
        lnSmokeVo.setDeviceNo( arg0.getDeviceNo() );
        lnSmokeVo.setDeviceName( arg0.getDeviceName() );
        lnSmokeVo.setEventCode( arg0.getEventCode() );
        lnSmokeVo.setEventContent( arg0.getEventContent() );
        lnSmokeVo.setEventTime( arg0.getEventTime() );
        lnSmokeVo.setDevNo( arg0.getDevNo() );
        lnSmokeVo.setCreateTime( arg0.getCreateTime() );

        return lnSmokeVo;
    }

    @Override
    public LnSmokeVo convert(LnSmoke arg0, LnSmokeVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDeviceNo( arg0.getDeviceNo() );
        arg1.setDeviceName( arg0.getDeviceName() );
        arg1.setEventCode( arg0.getEventCode() );
        arg1.setEventContent( arg0.getEventContent() );
        arg1.setEventTime( arg0.getEventTime() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
