package org.dromara.special.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.special.domain.SpecialEquipment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SpecialEquipmentVoToSpecialEquipmentMapperImpl implements SpecialEquipmentVoToSpecialEquipmentMapper {

    @Override
    public SpecialEquipment convert(SpecialEquipmentVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SpecialEquipment specialEquipment = new SpecialEquipment();

        specialEquipment.setEquipmentId( arg0.getEquipmentId() );
        specialEquipment.setCertificateNumber( arg0.getCertificateNumber() );
        specialEquipment.setIssuer( arg0.getIssuer() );
        specialEquipment.setIssueDate( arg0.getIssueDate() );
        specialEquipment.setUseRegistrationCertificate( arg0.getUseRegistrationCertificate() );
        specialEquipment.setEquipmentCategory( arg0.getEquipmentCategory() );
        specialEquipment.setModelSpec( arg0.getModelSpec() );
        specialEquipment.setFactoryNumber( arg0.getFactoryNumber() );
        specialEquipment.setRecordNumber( arg0.getRecordNumber() );
        specialEquipment.setManufacturer( arg0.getManufacturer() );
        specialEquipment.setPropertyOwner( arg0.getPropertyOwner() );
        specialEquipment.setProjectName( arg0.getProjectName() );
        specialEquipment.setProjectAddress( arg0.getProjectAddress() );
        specialEquipment.setProjectId( arg0.getProjectId() );
        specialEquipment.setItemId( arg0.getItemId() );
        specialEquipment.setUsageUnit( arg0.getUsageUnit() );
        specialEquipment.setMaintenanceUnit( arg0.getMaintenanceUnit() );
        specialEquipment.setInstallationUnit( arg0.getInstallationUnit() );
        specialEquipment.setInspectionUnit( arg0.getInspectionUnit() );
        specialEquipment.setProjectManager( arg0.getProjectManager() );
        specialEquipment.setInstallationDate( arg0.getInstallationDate() );
        specialEquipment.setInspectionDate( arg0.getInspectionDate() );
        specialEquipment.setEnterDate( arg0.getEnterDate() );
        specialEquipment.setExitDate( arg0.getExitDate() );
        specialEquipment.setLocation( arg0.getLocation() );
        specialEquipment.setSopId( arg0.getSopId() );
        specialEquipment.setRemarks( arg0.getRemarks() );
        specialEquipment.setDevNo( arg0.getDevNo() );

        return specialEquipment;
    }

    @Override
    public SpecialEquipment convert(SpecialEquipmentVo arg0, SpecialEquipment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setEquipmentId( arg0.getEquipmentId() );
        arg1.setCertificateNumber( arg0.getCertificateNumber() );
        arg1.setIssuer( arg0.getIssuer() );
        arg1.setIssueDate( arg0.getIssueDate() );
        arg1.setUseRegistrationCertificate( arg0.getUseRegistrationCertificate() );
        arg1.setEquipmentCategory( arg0.getEquipmentCategory() );
        arg1.setModelSpec( arg0.getModelSpec() );
        arg1.setFactoryNumber( arg0.getFactoryNumber() );
        arg1.setRecordNumber( arg0.getRecordNumber() );
        arg1.setManufacturer( arg0.getManufacturer() );
        arg1.setPropertyOwner( arg0.getPropertyOwner() );
        arg1.setProjectName( arg0.getProjectName() );
        arg1.setProjectAddress( arg0.getProjectAddress() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setUsageUnit( arg0.getUsageUnit() );
        arg1.setMaintenanceUnit( arg0.getMaintenanceUnit() );
        arg1.setInstallationUnit( arg0.getInstallationUnit() );
        arg1.setInspectionUnit( arg0.getInspectionUnit() );
        arg1.setProjectManager( arg0.getProjectManager() );
        arg1.setInstallationDate( arg0.getInstallationDate() );
        arg1.setInspectionDate( arg0.getInspectionDate() );
        arg1.setEnterDate( arg0.getEnterDate() );
        arg1.setExitDate( arg0.getExitDate() );
        arg1.setLocation( arg0.getLocation() );
        arg1.setSopId( arg0.getSopId() );
        arg1.setRemarks( arg0.getRemarks() );
        arg1.setDevNo( arg0.getDevNo() );

        return arg1;
    }
}
