package org.dromara.expert.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.expert.domain.Expert;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class ExpertBoToExpertMapperImpl implements ExpertBoToExpertMapper {

    @Override
    public Expert convert(ExpertBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Expert expert = new Expert();

        expert.setSearchValue( arg0.getSearchValue() );
        expert.setCreateDept( arg0.getCreateDept() );
        expert.setCreateBy( arg0.getCreateBy() );
        expert.setCreateTime( arg0.getCreateTime() );
        expert.setUpdateBy( arg0.getUpdateBy() );
        expert.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            expert.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        expert.setExpertId( arg0.getExpertId() );
        expert.setName( arg0.getName() );
        expert.setIdCard( arg0.getIdCard() );
        expert.setSex( arg0.getSex() );
        expert.setWorkUnit( arg0.getWorkUnit() );
        expert.setPhone( arg0.getPhone() );
        expert.setIntroduce( arg0.getIntroduce() );
        expert.setTitle( arg0.getTitle() );
        expert.setProvince( arg0.getProvince() );
        expert.setCity( arg0.getCity() );
        expert.setArea( arg0.getArea() );
        expert.setMajor( arg0.getMajor() );
        expert.setIndustry( arg0.getIndustry() );
        expert.setDelFlag( arg0.getDelFlag() );
        expert.setType( arg0.getType() );
        expert.setAvatar( arg0.getAvatar() );

        return expert;
    }

    @Override
    public Expert convert(ExpertBo arg0, Expert arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setExpertId( arg0.getExpertId() );
        arg1.setName( arg0.getName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setSex( arg0.getSex() );
        arg1.setWorkUnit( arg0.getWorkUnit() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setProvince( arg0.getProvince() );
        arg1.setCity( arg0.getCity() );
        arg1.setArea( arg0.getArea() );
        arg1.setMajor( arg0.getMajor() );
        arg1.setIndustry( arg0.getIndustry() );
        arg1.setDelFlag( arg0.getDelFlag() );
        arg1.setType( arg0.getType() );
        arg1.setAvatar( arg0.getAvatar() );

        return arg1;
    }
}
