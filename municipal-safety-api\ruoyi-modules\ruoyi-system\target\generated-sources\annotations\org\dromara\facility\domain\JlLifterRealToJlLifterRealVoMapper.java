package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.JlLifterRealBoToJlLifterRealMapper;
import org.dromara.facility.domain.vo.JlLifterRealVo;
import org.dromara.facility.domain.vo.JlLifterRealVoToJlLifterRealMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {JlLifterRealVoToJlLifterRealMapper.class,JlLifterRealBoToJlLifterRealMapper.class},
    imports = {}
)
public interface JlLifterRealToJlLifterRealVoMapper extends BaseMapper<JlLifterReal, JlLifterRealVo> {
}
