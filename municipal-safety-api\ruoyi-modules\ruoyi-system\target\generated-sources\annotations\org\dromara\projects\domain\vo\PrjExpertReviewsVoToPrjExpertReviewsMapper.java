package org.dromara.projects.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjExpertReviews;
import org.dromara.projects.domain.PrjExpertReviewsToPrjExpertReviewsVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {PrjExpertReviewsToPrjExpertReviewsVoMapper.class},
    imports = {}
)
public interface PrjExpertReviewsVoToPrjExpertReviewsMapper extends BaseMapper<PrjExpertReviewsVo, PrjExpertReviews> {
}
