package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.MonitorFacilityBoToMonitorFacilityMapper;
import org.dromara.facility.domain.vo.MonitorFacilityVo;
import org.dromara.facility.domain.vo.MonitorFacilityVoToMonitorFacilityMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {MonitorFacilityVoToMonitorFacilityMapper.class,MonitorFacilityBoToMonitorFacilityMapper.class},
    imports = {}
)
public interface MonitorFacilityToMonitorFacilityVoMapper extends BaseMapper<MonitorFacility, MonitorFacilityVo> {
}
