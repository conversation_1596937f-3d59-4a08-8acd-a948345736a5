package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.DangerList;
import org.dromara.system.domain.DangerListToDangerListVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {DangerListToDangerListVoMapper.class},
    imports = {}
)
public interface DangerListVoToDangerListMapper extends BaseMapper<DangerListVo, DangerList> {
}
