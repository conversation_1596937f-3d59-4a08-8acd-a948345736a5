package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.LnNutVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnNutToLnNutVoMapperImpl implements LnNutToLnNutVoMapper {

    @Override
    public LnNutVo convert(LnNut arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnNutVo lnNutVo = new LnNutVo();

        lnNutVo.setId( arg0.getId() );
        lnNutVo.setPara( arg0.getPara() );
        lnNutVo.setVt( arg0.getVt() );
        lnNutVo.setQds( arg0.getQds() );
        lnNutVo.setTime( arg0.getTime() );
        lnNutVo.setValue( arg0.getValue() );
        lnNutVo.setDevNo( arg0.getDevNo() );
        lnNutVo.setCreateTime( arg0.getCreateTime() );

        return lnNutVo;
    }

    @Override
    public LnNutVo convert(LnNut arg0, LnNutVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setPara( arg0.getPara() );
        arg1.setVt( arg0.getVt() );
        arg1.setQds( arg0.getQds() );
        arg1.setTime( arg0.getTime() );
        arg1.setValue( arg0.getValue() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
