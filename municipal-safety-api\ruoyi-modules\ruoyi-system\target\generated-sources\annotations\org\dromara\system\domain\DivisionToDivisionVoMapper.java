package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjProjectsToPrjProjectsVoMapper;
import org.dromara.projects.domain.vo.PrjProjectsVoToPrjProjectsMapper;
import org.dromara.system.domain.bo.DivisionBoToDivisionMapper;
import org.dromara.system.domain.vo.DivisionVo;
import org.dromara.system.domain.vo.DivisionVoToDivisionMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {PrjProjectsVoToPrjProjectsMapper.class,PrjProjectsToPrjProjectsVoMapper.class,DivisionBoToDivisionMapper.class,DivisionVoToDivisionMapper.class},
    imports = {}
)
public interface DivisionToDivisionVoMapper extends BaseMapper<Division, DivisionVo> {
}
