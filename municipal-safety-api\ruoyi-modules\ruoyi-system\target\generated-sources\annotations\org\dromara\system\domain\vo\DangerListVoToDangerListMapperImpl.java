package org.dromara.system.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.DangerList;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class DangerListVoToDangerListMapperImpl implements DangerListVoToDangerListMapper {

    @Override
    public DangerList convert(DangerListVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DangerList dangerList = new DangerList();

        dangerList.setDangerId( arg0.getDangerId() );
        dangerList.setName( arg0.getName() );
        dangerList.setPreId( arg0.getPreId() );
        dangerList.setType( arg0.getType() );
        dangerList.setRemark( arg0.getRemark() );

        return dangerList;
    }

    @Override
    public DangerList convert(DangerListVo arg0, DangerList arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setDangerId( arg0.getDangerId() );
        arg1.setName( arg0.getName() );
        arg1.setPreId( arg0.getPreId() );
        arg1.setType( arg0.getType() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
