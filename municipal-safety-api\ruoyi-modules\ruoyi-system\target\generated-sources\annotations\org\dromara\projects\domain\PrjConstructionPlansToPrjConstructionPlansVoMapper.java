package org.dromara.projects.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.bo.PrjConstructionPlansBoToPrjConstructionPlansMapper;
import org.dromara.projects.domain.vo.PrjConstructionPlansVo;
import org.dromara.projects.domain.vo.PrjConstructionPlansVoToPrjConstructionPlansMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {PrjConstructionPlansBoToPrjConstructionPlansMapper.class,PrjConstructionPlansVoToPrjConstructionPlansMapper.class},
    imports = {}
)
public interface PrjConstructionPlansToPrjConstructionPlansVoMapper extends BaseMapper<PrjConstructionPlans, PrjConstructionPlansVo> {
}
