package org.dromara.facility.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.facility.domain.LnNut;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnNutBoToLnNutMapperImpl implements LnNutBoToLnNutMapper {

    @Override
    public LnNut convert(LnNutBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnNut lnNut = new LnNut();

        lnNut.setSearchValue( arg0.getSearchValue() );
        lnNut.setCreateDept( arg0.getCreateDept() );
        lnNut.setCreateBy( arg0.getCreateBy() );
        lnNut.setUpdateBy( arg0.getUpdateBy() );
        lnNut.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            lnNut.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        lnNut.setId( arg0.getId() );
        lnNut.setPara( arg0.getPara() );
        lnNut.setVt( arg0.getVt() );
        lnNut.setQds( arg0.getQds() );
        lnNut.setTime( arg0.getTime() );
        lnNut.setValue( arg0.getValue() );
        lnNut.setDevNo( arg0.getDevNo() );
        lnNut.setCreateTime( arg0.getCreateTime() );

        return lnNut;
    }

    @Override
    public LnNut convert(LnNutBo arg0, LnNut arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setPara( arg0.getPara() );
        arg1.setVt( arg0.getVt() );
        arg1.setQds( arg0.getQds() );
        arg1.setTime( arg0.getTime() );
        arg1.setValue( arg0.getValue() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
