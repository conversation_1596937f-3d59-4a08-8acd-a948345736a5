package org.dromara.person.domain;

import javax.annotation.processing.Generated;
import org.dromara.person.domain.vo.QualificationDictVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class QualificationDictToQualificationDictVoMapperImpl implements QualificationDictToQualificationDictVoMapper {

    @Override
    public QualificationDictVo convert(QualificationDict arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QualificationDictVo qualificationDictVo = new QualificationDictVo();

        qualificationDictVo.setId( arg0.getId() );
        qualificationDictVo.setName( arg0.getName() );
        qualificationDictVo.setPreId( arg0.getPreId() );

        return qualificationDictVo;
    }

    @Override
    public QualificationDictVo convert(QualificationDict arg0, QualificationDictVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setPreId( arg0.getPreId() );

        return arg1;
    }
}
