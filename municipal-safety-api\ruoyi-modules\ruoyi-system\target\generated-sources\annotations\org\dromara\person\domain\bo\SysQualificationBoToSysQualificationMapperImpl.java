package org.dromara.person.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.person.domain.SysQualification;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SysQualificationBoToSysQualificationMapperImpl implements SysQualificationBoToSysQualificationMapper {

    @Override
    public SysQualification convert(SysQualificationBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysQualification sysQualification = new SysQualification();

        sysQualification.setSearchValue( arg0.getSearchValue() );
        sysQualification.setCreateDept( arg0.getCreateDept() );
        sysQualification.setCreateBy( arg0.getCreateBy() );
        sysQualification.setCreateTime( arg0.getCreateTime() );
        sysQualification.setUpdateBy( arg0.getUpdateBy() );
        sysQualification.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysQualification.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysQualification.setQualificationId( arg0.getQualificationId() );
        sysQualification.setPersonId( arg0.getPersonId() );
        sysQualification.setCertificateType( arg0.getCertificateType() );
        sysQualification.setCertificateName( arg0.getCertificateName() );
        sysQualification.setCertificateNumber( arg0.getCertificateNumber() );
        sysQualification.setAcquisitionTime( arg0.getAcquisitionTime() );
        sysQualification.setIssuingAuthority( arg0.getIssuingAuthority() );
        sysQualification.setCertificateLevel( arg0.getCertificateLevel() );
        sysQualification.setCorrespondingPosition( arg0.getCorrespondingPosition() );
        sysQualification.setValidFrom( arg0.getValidFrom() );
        sysQualification.setValidTo( arg0.getValidTo() );
        sysQualification.setUploadPhoto( arg0.getUploadPhoto() );

        return sysQualification;
    }

    @Override
    public SysQualification convert(SysQualificationBo arg0, SysQualification arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setQualificationId( arg0.getQualificationId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setCertificateType( arg0.getCertificateType() );
        arg1.setCertificateName( arg0.getCertificateName() );
        arg1.setCertificateNumber( arg0.getCertificateNumber() );
        arg1.setAcquisitionTime( arg0.getAcquisitionTime() );
        arg1.setIssuingAuthority( arg0.getIssuingAuthority() );
        arg1.setCertificateLevel( arg0.getCertificateLevel() );
        arg1.setCorrespondingPosition( arg0.getCorrespondingPosition() );
        arg1.setValidFrom( arg0.getValidFrom() );
        arg1.setValidTo( arg0.getValidTo() );
        arg1.setUploadPhoto( arg0.getUploadPhoto() );

        return arg1;
    }
}
