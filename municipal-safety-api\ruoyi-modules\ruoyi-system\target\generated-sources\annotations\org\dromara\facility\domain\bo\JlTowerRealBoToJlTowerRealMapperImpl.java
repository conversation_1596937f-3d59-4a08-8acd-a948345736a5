package org.dromara.facility.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.JlTowerReal;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class JlTowerRealBoToJlTowerRealMapperImpl implements JlTowerRealBoToJlTowerRealMapper {

    @Override
    public JlTowerReal convert(JlTowerRealBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        JlTowerReal jlTowerReal = new JlTowerReal();

        jlTowerReal.setId( arg0.getId() );
        jlTowerReal.setDevNo( arg0.getDevNo() );
        jlTowerReal.setTcNo( arg0.getTcNo() );
        jlTowerReal.setAddTime( arg0.getAddTime() );
        jlTowerReal.setLockMachineStatus( arg0.getLockMachineStatus() );
        jlTowerReal.setHeight( arg0.getHeight() );
        jlTowerReal.setRealRange( arg0.getRealRange() );
        jlTowerReal.setRotary( arg0.getRotary() );
        jlTowerReal.setStartWeight( arg0.getStartWeight() );
        jlTowerReal.setWindSpeed( arg0.getWindSpeed() );
        jlTowerReal.setSlant( arg0.getSlant() );
        jlTowerReal.setWeightPct( arg0.getWeightPct() );
        jlTowerReal.setMofPct( arg0.getMofPct() );
        jlTowerReal.setWindSpeedPct( arg0.getWindSpeedPct() );
        jlTowerReal.setSlantPct( arg0.getSlantPct() );
        jlTowerReal.setAlarmCause( arg0.getAlarmCause() );
        jlTowerReal.setAlarmCauseZh( arg0.getAlarmCauseZh() );
        jlTowerReal.setBrakingStatusUp( arg0.getBrakingStatusUp() );
        jlTowerReal.setBrakingStatusDown( arg0.getBrakingStatusDown() );
        jlTowerReal.setBrakingStatusFront( arg0.getBrakingStatusFront() );
        jlTowerReal.setBrakingStatusBack( arg0.getBrakingStatusBack() );
        jlTowerReal.setBrakingStatusLeft( arg0.getBrakingStatusLeft() );
        jlTowerReal.setBrakingStatusRight( arg0.getBrakingStatusRight() );
        jlTowerReal.setWorkingStatus( arg0.getWorkingStatus() );
        jlTowerReal.setCreateTime( arg0.getCreateTime() );

        return jlTowerReal;
    }

    @Override
    public JlTowerReal convert(JlTowerRealBo arg0, JlTowerReal arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setTcNo( arg0.getTcNo() );
        arg1.setAddTime( arg0.getAddTime() );
        arg1.setLockMachineStatus( arg0.getLockMachineStatus() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setRealRange( arg0.getRealRange() );
        arg1.setRotary( arg0.getRotary() );
        arg1.setStartWeight( arg0.getStartWeight() );
        arg1.setWindSpeed( arg0.getWindSpeed() );
        arg1.setSlant( arg0.getSlant() );
        arg1.setWeightPct( arg0.getWeightPct() );
        arg1.setMofPct( arg0.getMofPct() );
        arg1.setWindSpeedPct( arg0.getWindSpeedPct() );
        arg1.setSlantPct( arg0.getSlantPct() );
        arg1.setAlarmCause( arg0.getAlarmCause() );
        arg1.setAlarmCauseZh( arg0.getAlarmCauseZh() );
        arg1.setBrakingStatusUp( arg0.getBrakingStatusUp() );
        arg1.setBrakingStatusDown( arg0.getBrakingStatusDown() );
        arg1.setBrakingStatusFront( arg0.getBrakingStatusFront() );
        arg1.setBrakingStatusBack( arg0.getBrakingStatusBack() );
        arg1.setBrakingStatusLeft( arg0.getBrakingStatusLeft() );
        arg1.setBrakingStatusRight( arg0.getBrakingStatusRight() );
        arg1.setWorkingStatus( arg0.getWorkingStatus() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
