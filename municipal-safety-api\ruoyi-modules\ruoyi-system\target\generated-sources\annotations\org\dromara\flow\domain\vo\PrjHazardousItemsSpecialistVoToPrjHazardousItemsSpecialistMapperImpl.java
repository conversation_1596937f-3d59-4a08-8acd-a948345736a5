package org.dromara.flow.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.flow.domain.PrjHazardousItemsSpecialist;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsSpecialistVoToPrjHazardousItemsSpecialistMapperImpl implements PrjHazardousItemsSpecialistVoToPrjHazardousItemsSpecialistMapper {

    @Override
    public PrjHazardousItemsSpecialist convert(PrjHazardousItemsSpecialistVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsSpecialist prjHazardousItemsSpecialist = new PrjHazardousItemsSpecialist();

        prjHazardousItemsSpecialist.setId( arg0.getId() );
        prjHazardousItemsSpecialist.setSpecialist( arg0.getSpecialist() );
        prjHazardousItemsSpecialist.setInstruction( arg0.getInstruction() );
        prjHazardousItemsSpecialist.setDownPushFile( arg0.getDownPushFile() );
        prjHazardousItemsSpecialist.setTaskId( arg0.getTaskId() );

        return prjHazardousItemsSpecialist;
    }

    @Override
    public PrjHazardousItemsSpecialist convert(PrjHazardousItemsSpecialistVo arg0, PrjHazardousItemsSpecialist arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setSpecialist( arg0.getSpecialist() );
        arg1.setInstruction( arg0.getInstruction() );
        arg1.setDownPushFile( arg0.getDownPushFile() );
        arg1.setTaskId( arg0.getTaskId() );

        return arg1;
    }
}
