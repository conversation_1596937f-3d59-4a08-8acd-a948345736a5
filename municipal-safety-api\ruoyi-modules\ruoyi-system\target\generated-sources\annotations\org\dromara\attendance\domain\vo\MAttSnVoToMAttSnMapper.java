package org.dromara.attendance.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.attendance.domain.MAttSn;
import org.dromara.attendance.domain.MAttSnToMAttSnVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {MAttSnToMAttSnVoMapper.class},
    imports = {}
)
public interface MAttSnVoToMAttSnMapper extends BaseMapper<MAttSnVo, MAttSn> {
}
