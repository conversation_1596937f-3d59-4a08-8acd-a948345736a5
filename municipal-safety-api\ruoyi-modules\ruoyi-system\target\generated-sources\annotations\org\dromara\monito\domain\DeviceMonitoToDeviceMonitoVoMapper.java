package org.dromara.monito.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.monito.domain.bo.DeviceMonitoBoToDeviceMonitoMapper;
import org.dromara.monito.domain.vo.DeviceMonitoVo;
import org.dromara.monito.domain.vo.DeviceMonitoVoToDeviceMonitoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {DeviceMonitoVoToDeviceMonitoMapper.class,DeviceMonitoBoToDeviceMonitoMapper.class},
    imports = {}
)
public interface DeviceMonitoToDeviceMonitoVoMapper extends BaseMapper<DeviceMonito, DeviceMonitoVo> {
}
