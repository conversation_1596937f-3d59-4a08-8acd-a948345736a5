package org.dromara.facility.domain;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.vo.LnSprayingVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnSprayingToLnSprayingVoMapperImpl implements LnSprayingToLnSprayingVoMapper {

    @Override
    public LnSprayingVo convert(LnSpraying arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnSprayingVo lnSprayingVo = new LnSprayingVo();

        lnSprayingVo.setId( arg0.getId() );
        lnSprayingVo.setRelayStatus( arg0.getRelayStatus() );
        lnSprayingVo.setWorkMode( arg0.getWorkMode() );
        lnSprayingVo.setWaterSensor( arg0.getWaterSensor() );
        lnSprayingVo.setStatus( arg0.getStatus() );
        lnSprayingVo.setDevNo( arg0.getDevNo() );
        lnSprayingVo.setCreateTime( arg0.getCreateTime() );

        return lnSprayingVo;
    }

    @Override
    public LnSprayingVo convert(LnSpraying arg0, LnSprayingVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setRelayStatus( arg0.getRelayStatus() );
        arg1.setWorkMode( arg0.getWorkMode() );
        arg1.setWaterSensor( arg0.getWaterSensor() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
