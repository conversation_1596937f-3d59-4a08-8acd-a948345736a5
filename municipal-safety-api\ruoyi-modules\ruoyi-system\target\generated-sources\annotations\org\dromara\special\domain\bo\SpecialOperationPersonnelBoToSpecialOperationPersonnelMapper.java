package org.dromara.special.domain.bo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.special.domain.SpecialOperationPersonnel;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {},
    imports = {}
)
public interface SpecialOperationPersonnelBoToSpecialOperationPersonnelMapper extends BaseMapper<SpecialOperationPersonnelBo, SpecialOperationPersonnel> {
}
