package org.dromara.facility.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.LnWater;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnWaterBoToLnWaterMapperImpl implements LnWaterBoToLnWaterMapper {

    @Override
    public LnWater convert(LnWaterBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnWater lnWater = new LnWater();

        lnWater.setId( arg0.getId() );
        lnWater.setSdds( arg0.getSdds() );
        lnWater.setRecordTime( arg0.getRecordTime() );
        lnWater.setSylj( arg0.getSylj() );
        lnWater.setZrlj( arg0.getZrlj() );
        lnWater.setQyxslj( arg0.getQyxslj() );
        lnWater.setDqxsnlj( arg0.getDqxsnlj() );
        lnWater.setSsls( arg0.getSsls() );
        lnWater.setDczt( arg0.getDczt() );
        lnWater.setDcdy( arg0.getDcdy() );
        lnWater.setDcdl( arg0.getDcdl() );
        lnWater.setBl( arg0.getBl() );
        lnWater.setDevNo( arg0.getDevNo() );
        lnWater.setCreateTime( arg0.getCreateTime() );

        return lnWater;
    }

    @Override
    public LnWater convert(LnWaterBo arg0, LnWater arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setSdds( arg0.getSdds() );
        arg1.setRecordTime( arg0.getRecordTime() );
        arg1.setSylj( arg0.getSylj() );
        arg1.setZrlj( arg0.getZrlj() );
        arg1.setQyxslj( arg0.getQyxslj() );
        arg1.setDqxsnlj( arg0.getDqxsnlj() );
        arg1.setSsls( arg0.getSsls() );
        arg1.setDczt( arg0.getDczt() );
        arg1.setDcdy( arg0.getDcdy() );
        arg1.setDcdl( arg0.getDcdl() );
        arg1.setBl( arg0.getBl() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
