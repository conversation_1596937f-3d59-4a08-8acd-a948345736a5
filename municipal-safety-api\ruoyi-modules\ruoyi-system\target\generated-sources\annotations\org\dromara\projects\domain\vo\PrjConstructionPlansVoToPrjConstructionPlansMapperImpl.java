package org.dromara.projects.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjConstructionPlans;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjConstructionPlansVoToPrjConstructionPlansMapperImpl implements PrjConstructionPlansVoToPrjConstructionPlansMapper {

    @Override
    public PrjConstructionPlans convert(PrjConstructionPlansVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjConstructionPlans prjConstructionPlans = new PrjConstructionPlans();

        prjConstructionPlans.setPlanId( arg0.getPlanId() );
        prjConstructionPlans.setItemId( arg0.getItemId() );
        prjConstructionPlans.setPlanName( arg0.getPlanName() );
        prjConstructionPlans.setPlanVersion( arg0.getPlanVersion() );
        prjConstructionPlans.setPlanDocumentId( arg0.getPlanDocumentId() );
        prjConstructionPlans.setApprovalFormDocId( arg0.getApprovalFormDocId() );
        prjConstructionPlans.setReviewStatus( arg0.getReviewStatus() );
        prjConstructionPlans.setAiDefectWarningDetails( arg0.getAiDefectWarningDetails() );

        return prjConstructionPlans;
    }

    @Override
    public PrjConstructionPlans convert(PrjConstructionPlansVo arg0, PrjConstructionPlans arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setPlanId( arg0.getPlanId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setPlanName( arg0.getPlanName() );
        arg1.setPlanVersion( arg0.getPlanVersion() );
        arg1.setPlanDocumentId( arg0.getPlanDocumentId() );
        arg1.setApprovalFormDocId( arg0.getApprovalFormDocId() );
        arg1.setReviewStatus( arg0.getReviewStatus() );
        arg1.setAiDefectWarningDetails( arg0.getAiDefectWarningDetails() );

        return arg1;
    }
}
