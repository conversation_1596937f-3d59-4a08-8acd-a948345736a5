package org.dromara.person.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.person.domain.QualificationDict;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class QualificationDictVoToQualificationDictMapperImpl implements QualificationDictVoToQualificationDictMapper {

    @Override
    public QualificationDict convert(QualificationDictVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QualificationDict qualificationDict = new QualificationDict();

        qualificationDict.setId( arg0.getId() );
        qualificationDict.setName( arg0.getName() );
        qualificationDict.setPreId( arg0.getPreId() );

        return qualificationDict;
    }

    @Override
    public QualificationDict convert(QualificationDictVo arg0, QualificationDict arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setPreId( arg0.getPreId() );

        return arg1;
    }
}
