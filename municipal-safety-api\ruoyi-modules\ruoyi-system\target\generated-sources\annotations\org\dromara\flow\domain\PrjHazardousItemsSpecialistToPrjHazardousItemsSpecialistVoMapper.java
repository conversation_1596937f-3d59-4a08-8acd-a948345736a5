package org.dromara.flow.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialistBoToPrjHazardousItemsSpecialistMapper;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistVo;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistVoToPrjHazardousItemsSpecialistMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {PrjHazardousItemsSpecialistBoToPrjHazardousItemsSpecialistMapper.class,PrjHazardousItemsSpecialistVoToPrjHazardousItemsSpecialistMapper.class},
    imports = {}
)
public interface PrjHazardousItemsSpecialistToPrjHazardousItemsSpecialistVoMapper extends BaseMapper<PrjHazardousItemsSpecialist, PrjHazardousItemsSpecialistVo> {
}
