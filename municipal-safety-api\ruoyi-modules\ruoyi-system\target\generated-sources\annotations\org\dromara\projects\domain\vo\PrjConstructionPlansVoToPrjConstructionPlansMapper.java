package org.dromara.projects.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.projects.domain.PrjConstructionPlans;
import org.dromara.projects.domain.PrjConstructionPlansToPrjConstructionPlansVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {PrjConstructionPlansToPrjConstructionPlansVoMapper.class},
    imports = {}
)
public interface PrjConstructionPlansVoToPrjConstructionPlansMapper extends BaseMapper<PrjConstructionPlansVo, PrjConstructionPlans> {
}
