package org.dromara.expert.domain;

import javax.annotation.processing.Generated;
import org.dromara.expert.domain.vo.ExpertVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class ExpertToExpertVoMapperImpl implements ExpertToExpertVoMapper {

    @Override
    public ExpertVo convert(Expert arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ExpertVo expertVo = new ExpertVo();

        expertVo.setExpertId( arg0.getExpertId() );
        expertVo.setName( arg0.getName() );
        expertVo.setIdCard( arg0.getIdCard() );
        expertVo.setSex( arg0.getSex() );
        expertVo.setWorkUnit( arg0.getWorkUnit() );
        expertVo.setPhone( arg0.getPhone() );
        expertVo.setIntroduce( arg0.getIntroduce() );
        expertVo.setTitle( arg0.getTitle() );
        expertVo.setProvince( arg0.getProvince() );
        expertVo.setCity( arg0.getCity() );
        expertVo.setArea( arg0.getArea() );
        expertVo.setMajor( arg0.getMajor() );
        expertVo.setIndustry( arg0.getIndustry() );
        expertVo.setType( arg0.getType() );
        expertVo.setAvatar( arg0.getAvatar() );

        return expertVo;
    }

    @Override
    public ExpertVo convert(Expert arg0, ExpertVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setExpertId( arg0.getExpertId() );
        arg1.setName( arg0.getName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setSex( arg0.getSex() );
        arg1.setWorkUnit( arg0.getWorkUnit() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setProvince( arg0.getProvince() );
        arg1.setCity( arg0.getCity() );
        arg1.setArea( arg0.getArea() );
        arg1.setMajor( arg0.getMajor() );
        arg1.setIndustry( arg0.getIndustry() );
        arg1.setType( arg0.getType() );
        arg1.setAvatar( arg0.getAvatar() );

        return arg1;
    }
}
