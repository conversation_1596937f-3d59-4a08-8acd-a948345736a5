package org.dromara.attendance.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.attendance.domain.bo.MAttRuleBoToMAttRuleMapper;
import org.dromara.attendance.domain.vo.MAttRuleVo;
import org.dromara.attendance.domain.vo.MAttRuleVoToMAttRuleMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {MAttRuleBoToMAttRuleMapper.class,MAttRuleVoToMAttRuleMapper.class},
    imports = {}
)
public interface MAttRuleToMAttRuleVoMapper extends BaseMapper<MAttRule, MAttRuleVo> {
}
