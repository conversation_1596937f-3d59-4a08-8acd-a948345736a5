package org.dromara.ai.domain.bo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {},
    imports = {}
)
public interface AiHazAnalysisTasksResultBoToAiHazAnalysisTasksResultMapper extends BaseMapper<AiHazAnalysisTasksResultBo, AiHazAnalysisTasksResult> {
}
