package org.dromara.flow.domain;

import javax.annotation.processing.Generated;
import org.dromara.flow.domain.vo.PrjHazardousItemsFileVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsFileToPrjHazardousItemsFileVoMapperImpl implements PrjHazardousItemsFileToPrjHazardousItemsFileVoMapper {

    @Override
    public PrjHazardousItemsFileVo convert(PrjHazardousItemsFile arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsFileVo prjHazardousItemsFileVo = new PrjHazardousItemsFileVo();

        prjHazardousItemsFileVo.setItemFileId( arg0.getItemFileId() );
        prjHazardousItemsFileVo.setName( arg0.getName() );
        prjHazardousItemsFileVo.setFileId( arg0.getFileId() );
        prjHazardousItemsFileVo.setTaskId( arg0.getTaskId() );
        prjHazardousItemsFileVo.setServiceType( arg0.getServiceType() );
        prjHazardousItemsFileVo.setCallFileId( arg0.getCallFileId() );

        return prjHazardousItemsFileVo;
    }

    @Override
    public PrjHazardousItemsFileVo convert(PrjHazardousItemsFile arg0, PrjHazardousItemsFileVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setItemFileId( arg0.getItemFileId() );
        arg1.setName( arg0.getName() );
        arg1.setFileId( arg0.getFileId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setServiceType( arg0.getServiceType() );
        arg1.setCallFileId( arg0.getCallFileId() );

        return arg1;
    }
}
