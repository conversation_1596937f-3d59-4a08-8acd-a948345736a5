package org.dromara.plan.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.expert.domain.ExpertToExpertVoMapper;
import org.dromara.expert.domain.vo.ExpertVoToExpertMapper;
import org.dromara.plan.domain.PrjPatrolPlan;
import org.dromara.plan.domain.PrjPatrolPlanToPrjPatrolPlanVoMapper;
import org.dromara.projects.domain.PrjProjectsToPrjProjectsVoMapper;
import org.dromara.projects.domain.vo.PrjProjectsVoToPrjProjectsMapper;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {SysDeptVoToSysDeptMapper.class,SysDeptToSysDeptVoMapper.class,ExpertVoToExpertMapper.class,ExpertToExpertVoMapper.class,PrjProjectsVoToPrjProjectsMapper.class,PrjProjectsToPrjProjectsVoMapper.class,PrjPatrolPlanToPrjPatrolPlanVoMapper.class},
    imports = {}
)
public interface PrjPatrolPlanVoToPrjPatrolPlanMapper extends BaseMapper<PrjPatrolPlanVo, PrjPatrolPlan> {
}
