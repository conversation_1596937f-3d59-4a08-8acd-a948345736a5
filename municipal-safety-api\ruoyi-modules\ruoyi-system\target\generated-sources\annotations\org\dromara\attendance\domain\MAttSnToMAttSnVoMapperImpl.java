package org.dromara.attendance.domain;

import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.vo.MAttSnVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttSnToMAttSnVoMapperImpl implements MAttSnToMAttSnVoMapper {

    @Override
    public MAttSnVo convert(MAttSn arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttSnVo mAttSnVo = new MAttSnVo();

        mAttSnVo.setSnId( arg0.getSnId() );
        mAttSnVo.setProjectId( arg0.getProjectId() );
        mAttSnVo.setSn( arg0.getSn() );
        mAttSnVo.setSnName( arg0.getSnName() );
        mAttSnVo.setDirection( arg0.getDirection() );
        mAttSnVo.setStatus( arg0.getStatus() );

        return mAttSnVo;
    }

    @Override
    public MAttSnVo convert(MAttSn arg0, MAttSnVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSnId( arg0.getSnId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setSn( arg0.getSn() );
        arg1.setSnName( arg0.getSnName() );
        arg1.setDirection( arg0.getDirection() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
