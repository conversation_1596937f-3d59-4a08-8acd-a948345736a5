# 项目同步权限验证功能说明

## 概述

本文档描述了为项目同步功能添加的权限验证逻辑，确保施工方用户只能同步属于自己公司的项目，防止跨公司数据访问违规。

## 功能特性

### 1. 用户身份验证
- 验证当前用户是否已登录
- 获取用户的部门信息和企业归属

### 2. 施工方用户识别
- 检查用户所属部门的 `deptType` 字段
- 支持多类型部门（如 "CLIENT,CONSTRUCTION,SUPERVISION"）
- 只有包含 "CONSTRUCTION" 类型的部门用户才被视为施工方用户

### 3. 项目归属验证
- **现有项目验证**：检查项目的 `constructionOrgId` 是否与用户的 `deptId` 匹配
- **新项目预验证**：从外部API获取项目数据，验证用户公司是否为该项目的施工单位

### 4. 权限控制策略
- **施工方用户**：只能同步属于自己公司的项目
- **非施工方用户**：允许同步任何项目（管理员、监管部门等）

## 实现细节

### 核心验证方法

#### `validateUserPermissionForSync(String constructionPermitNo)`
主要的权限验证入口，协调整个验证流程。

#### `isConstructionCompanyUser(LoginUser loginUser)`
判断用户是否属于施工方：
```java
// 检查部门类型是否包含 CONSTRUCTION
String deptType = userDept.getDeptType();
return deptType.contains("CONSTRUCTION");
```

#### `validateProjectOwnership(LoginUser loginUser, String constructionPermitNo)`
验证项目归属关系：
- 查找现有项目并检查 `constructionOrgId`
- 如果项目不存在，调用 `preValidateProjectOwnershipFromApi`

#### `preValidateProjectOwnershipFromApi(LoginUser loginUser, String constructionPermitNo)`
从外部API预验证项目归属：
- 获取用户公司的统一社会信用代码
- 调用外部API获取项目参建单位信息
- 验证用户公司是否为该项目的施工单位

## 验证流程

```mermaid
graph TD
    A[开始同步] --> B[验证施工许可证编号格式]
    B --> C[验证用户权限]
    C --> D{用户已登录?}
    D -->|否| E[返回未登录错误]
    D -->|是| F{是施工方用户?}
    F -->|否| G[允许同步 - 管理员/监管用户]
    F -->|是| H{项目已存在?}
    H -->|是| I[检查项目归属]
    H -->|否| J[从API预验证归属]
    I --> K{项目属于用户公司?}
    J --> L{API验证通过?}
    K -->|否| M[返回权限错误]
    L -->|否| M
    K -->|是| N[继续同步流程]
    L -->|是| N
    G --> N
    N --> O[获取外部API数据]
    O --> P[验证数据完整性]
    P --> Q[处理同步数据]
    Q --> R[同步完成]
```

## 错误消息

### 用户相关错误
- `"用户未登录，无法进行项目同步"`
- `"无法获取用户所属公司的统一社会信用代码"`

### 权限相关错误
- `"无权限同步此项目：该项目不属于您的施工公司"`
- `"无权限同步此项目：您的公司不是该项目的施工单位"`

### 数据相关错误
- `"无法从外部API获取项目数据进行权限验证"`
- `"项目数据中未找到参建单位信息"`

## API端点

### 同步项目
```
GET /projects/prj_projects/syncProject/{constructionPermitNo}
```

**权限要求**：`projects:prj_projects:query`

**参数**：
- `constructionPermitNo`：施工许可证编号（路径参数）

**响应**：
```json
{
  "code": 200,
  "msg": "同步成功",
  "data": {
    "projectId": 12345,
    "projectName": "测试项目",
    "constructionPermitNo": "TEST123456789",
    // ... 其他项目信息
  }
}
```

**错误响应示例**：
```json
{
  "code": 500,
  "msg": "无权限同步此项目：该项目不属于您的施工公司",
  "data": null
}
```

## 测试场景

### 1. 施工方用户同步自己公司的项目
- **预期结果**：同步成功
- **验证点**：用户部门ID与项目的constructionOrgId匹配

### 2. 施工方用户尝试同步其他公司的项目
- **预期结果**：权限验证失败
- **错误消息**：无权限同步此项目

### 3. 管理员用户同步任意项目
- **预期结果**：同步成功
- **验证点**：非施工方用户不受项目归属限制

### 4. 未登录用户尝试同步
- **预期结果**：权限验证失败
- **错误消息**：用户未登录

## 数据库关系

### 关键表和字段
- `sys_user.dept_id` → `sys_dept.dept_id`
- `sys_dept.dept_type`：部门类型（如 "CONSTRUCTION"）
- `sys_dept.dept_code`：统一社会信用代码
- `prj_projects.construction_org_id` → `sys_dept.dept_id`

### 企业类型映射
- "建设单位" → "CLIENT"
- "施工单位" → "CONSTRUCTION"
- "监理单位" → "SUPERVISION"
- "设计单位" → "DESIGN"
- "勘察单位" → "SURVEY"

## 配置说明

### 外部API配置
```java
private static final String EXTERNAL_API_URL = "https://zhaj.zjt.gansu.gov.cn:10124/dataexchangeserver/GGBApi/getGCXMInfo";
private static final String API_SECRET_KEY = "1211f5ea378f4f858ba796336e01ab78";
```

### 日志配置
验证过程中会记录详细的日志信息，包括：
- 用户权限验证结果
- 项目归属检查过程
- API调用和响应状态

## 安全考虑

1. **最小权限原则**：施工方用户只能访问自己公司的项目
2. **数据隔离**：通过部门ID实现数据访问控制
3. **审计日志**：记录所有权限验证操作
4. **错误处理**：避免泄露敏感信息的错误消息

## 维护说明

### 添加新的企业类型
1. 在 `mapCorpTypeToEnterpriseType` 方法中添加映射
2. 更新相关的角色配置
3. 测试新类型的权限验证逻辑

### 修改权限策略
如需修改权限控制策略，主要修改以下方法：
- `isConstructionCompanyUser`：用户类型判断逻辑
- `validateProjectOwnership`：项目归属验证逻辑
- `preValidateProjectOwnershipFromApi`：API预验证逻辑
