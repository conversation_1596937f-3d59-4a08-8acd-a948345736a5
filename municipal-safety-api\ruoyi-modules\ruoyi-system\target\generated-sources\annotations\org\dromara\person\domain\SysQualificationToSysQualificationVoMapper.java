package org.dromara.person.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.person.domain.bo.SysQualificationBoToSysQualificationMapper;
import org.dromara.person.domain.vo.SysQualificationVo;
import org.dromara.person.domain.vo.SysQualificationVoToSysQualificationMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {SysQualificationBoToSysQualificationMapper.class,SysQualificationVoToSysQualificationMapper.class},
    imports = {}
)
public interface SysQualificationToSysQualificationVoMapper extends BaseMapper<SysQualification, SysQualificationVo> {
}
