package org.dromara.special.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.special.domain.SpecialOperationPersonnel;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class SpecialOperationPersonnelVoToSpecialOperationPersonnelMapperImpl implements SpecialOperationPersonnelVoToSpecialOperationPersonnelMapper {

    @Override
    public SpecialOperationPersonnel convert(SpecialOperationPersonnelVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SpecialOperationPersonnel specialOperationPersonnel = new SpecialOperationPersonnel();

        specialOperationPersonnel.setSopId( arg0.getSopId() );
        specialOperationPersonnel.setCertificateNumber( arg0.getCertificateNumber() );
        specialOperationPersonnel.setName( arg0.getName() );
        specialOperationPersonnel.setIdCard( arg0.getIdCard() );
        specialOperationPersonnel.setGender( arg0.getGender() );
        specialOperationPersonnel.setBirthdate( arg0.getBirthdate() );
        specialOperationPersonnel.setOperationCategory( arg0.getOperationCategory() );
        specialOperationPersonnel.setIssuer( arg0.getIssuer() );
        specialOperationPersonnel.setFirstIssueDate( arg0.getFirstIssueDate() );
        specialOperationPersonnel.setLastIssueDate( arg0.getLastIssueDate() );
        specialOperationPersonnel.setValidityStart( arg0.getValidityStart() );
        specialOperationPersonnel.setValidityEnd( arg0.getValidityEnd() );
        specialOperationPersonnel.setStatus( arg0.getStatus() );
        specialOperationPersonnel.setElectronicLicenseUrl( arg0.getElectronicLicenseUrl() );
        specialOperationPersonnel.setElectronicLicenseId( arg0.getElectronicLicenseId() );
        specialOperationPersonnel.setProjectId( arg0.getProjectId() );

        return specialOperationPersonnel;
    }

    @Override
    public SpecialOperationPersonnel convert(SpecialOperationPersonnelVo arg0, SpecialOperationPersonnel arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSopId( arg0.getSopId() );
        arg1.setCertificateNumber( arg0.getCertificateNumber() );
        arg1.setName( arg0.getName() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setGender( arg0.getGender() );
        arg1.setBirthdate( arg0.getBirthdate() );
        arg1.setOperationCategory( arg0.getOperationCategory() );
        arg1.setIssuer( arg0.getIssuer() );
        arg1.setFirstIssueDate( arg0.getFirstIssueDate() );
        arg1.setLastIssueDate( arg0.getLastIssueDate() );
        arg1.setValidityStart( arg0.getValidityStart() );
        arg1.setValidityEnd( arg0.getValidityEnd() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setElectronicLicenseUrl( arg0.getElectronicLicenseUrl() );
        arg1.setElectronicLicenseId( arg0.getElectronicLicenseId() );
        arg1.setProjectId( arg0.getProjectId() );

        return arg1;
    }
}
