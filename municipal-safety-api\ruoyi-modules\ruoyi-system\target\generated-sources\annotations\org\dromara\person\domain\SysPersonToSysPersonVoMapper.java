package org.dromara.person.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.person.domain.bo.SysPersonBoToSysPersonMapper;
import org.dromara.person.domain.vo.SysPersonVo;
import org.dromara.person.domain.vo.SysPersonVoToSysPersonMapper;
import org.dromara.person.domain.vo.SysQualificationVoToSysQualificationMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {SysQualificationVoToSysQualificationMapper.class,SysQualificationToSysQualificationVoMapper.class,SysPersonBoToSysPersonMapper.class,SysPersonVoToSysPersonMapper.class},
    imports = {}
)
public interface SysPersonToSysPersonVoMapper extends BaseMapper<SysPerson, SysPersonVo> {
}
