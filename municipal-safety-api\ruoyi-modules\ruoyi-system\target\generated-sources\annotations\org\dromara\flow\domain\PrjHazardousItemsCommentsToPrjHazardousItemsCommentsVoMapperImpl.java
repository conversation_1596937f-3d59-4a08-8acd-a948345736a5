package org.dromara.flow.domain;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.flow.domain.vo.PrjHazardousItemsCommentsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsCommentsToPrjHazardousItemsCommentsVoMapperImpl implements PrjHazardousItemsCommentsToPrjHazardousItemsCommentsVoMapper {

    @Override
    public PrjHazardousItemsCommentsVo convert(PrjHazardousItemsComments arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsCommentsVo prjHazardousItemsCommentsVo = new PrjHazardousItemsCommentsVo();

        prjHazardousItemsCommentsVo.setId( arg0.getId() );
        prjHazardousItemsCommentsVo.setQuestion( arg0.getQuestion() );
        prjHazardousItemsCommentsVo.setTimeLimit( arg0.getTimeLimit() );
        prjHazardousItemsCommentsVo.setTimeType( arg0.getTimeType() );
        prjHazardousItemsCommentsVo.setCorrectionsFile( arg0.getCorrectionsFile() );
        prjHazardousItemsCommentsVo.setCorrectionsContent( arg0.getCorrectionsContent() );
        prjHazardousItemsCommentsVo.setCorrectionsBackFile( arg0.getCorrectionsBackFile() );
        prjHazardousItemsCommentsVo.setSuspensionFile( arg0.getSuspensionFile() );
        prjHazardousItemsCommentsVo.setSuspensionContent( arg0.getSuspensionContent() );
        prjHazardousItemsCommentsVo.setSuspensionBackFile( arg0.getSuspensionBackFile() );
        prjHazardousItemsCommentsVo.setPenaltyFile( arg0.getPenaltyFile() );
        prjHazardousItemsCommentsVo.setPenaltyContent( arg0.getPenaltyContent() );
        prjHazardousItemsCommentsVo.setPenaltyBackFile( arg0.getPenaltyBackFile() );
        prjHazardousItemsCommentsVo.setTaskId( arg0.getTaskId() );
        prjHazardousItemsCommentsVo.setCreateTime( arg0.getCreateTime() );
        List<PrjHazardousItemsFile> list = arg0.getElseFile();
        if ( list != null ) {
            prjHazardousItemsCommentsVo.setElseFile( new ArrayList<PrjHazardousItemsFile>( list ) );
        }

        return prjHazardousItemsCommentsVo;
    }

    @Override
    public PrjHazardousItemsCommentsVo convert(PrjHazardousItemsComments arg0, PrjHazardousItemsCommentsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setQuestion( arg0.getQuestion() );
        arg1.setTimeLimit( arg0.getTimeLimit() );
        arg1.setTimeType( arg0.getTimeType() );
        arg1.setCorrectionsFile( arg0.getCorrectionsFile() );
        arg1.setCorrectionsContent( arg0.getCorrectionsContent() );
        arg1.setCorrectionsBackFile( arg0.getCorrectionsBackFile() );
        arg1.setSuspensionFile( arg0.getSuspensionFile() );
        arg1.setSuspensionContent( arg0.getSuspensionContent() );
        arg1.setSuspensionBackFile( arg0.getSuspensionBackFile() );
        arg1.setPenaltyFile( arg0.getPenaltyFile() );
        arg1.setPenaltyContent( arg0.getPenaltyContent() );
        arg1.setPenaltyBackFile( arg0.getPenaltyBackFile() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getElseFile() != null ) {
            List<PrjHazardousItemsFile> list = arg0.getElseFile();
            if ( list != null ) {
                arg1.getElseFile().clear();
                arg1.getElseFile().addAll( list );
            }
            else {
                arg1.setElseFile( null );
            }
        }
        else {
            List<PrjHazardousItemsFile> list = arg0.getElseFile();
            if ( list != null ) {
                arg1.setElseFile( new ArrayList<PrjHazardousItemsFile>( list ) );
            }
        }

        return arg1;
    }
}
