package org.dromara.attendance.domain;

import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.vo.MAttRecordVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttRecordToMAttRecordVoMapperImpl implements MAttRecordToMAttRecordVoMapper {

    @Override
    public MAttRecordVo convert(MAttRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttRecordVo mAttRecordVo = new MAttRecordVo();

        mAttRecordVo.setId( arg0.getId() );
        mAttRecordVo.setRuleId( arg0.getRuleId() );
        mAttRecordVo.setPersonId( arg0.getPersonId() );
        mAttRecordVo.setPersonType( arg0.getPersonType() );
        mAttRecordVo.setRealName( arg0.getRealName() );
        mAttRecordVo.setIdNumber( arg0.getIdNumber() );
        mAttRecordVo.setRealTimeFace( arg0.getRealTimeFace() );
        mAttRecordVo.setSn( arg0.getSn() );
        mAttRecordVo.setSource( arg0.getSource() );
        mAttRecordVo.setContent( arg0.getContent() );
        mAttRecordVo.setAttTime( arg0.getAttTime() );
        mAttRecordVo.setAttDate( arg0.getAttDate() );
        mAttRecordVo.setAttResult( arg0.getAttResult() );
        mAttRecordVo.setWhichTime( arg0.getWhichTime() );

        return mAttRecordVo;
    }

    @Override
    public MAttRecordVo convert(MAttRecord arg0, MAttRecordVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setRuleId( arg0.getRuleId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setPersonType( arg0.getPersonType() );
        arg1.setRealName( arg0.getRealName() );
        arg1.setIdNumber( arg0.getIdNumber() );
        arg1.setRealTimeFace( arg0.getRealTimeFace() );
        arg1.setSn( arg0.getSn() );
        arg1.setSource( arg0.getSource() );
        arg1.setContent( arg0.getContent() );
        arg1.setAttTime( arg0.getAttTime() );
        arg1.setAttDate( arg0.getAttDate() );
        arg1.setAttResult( arg0.getAttResult() );
        arg1.setWhichTime( arg0.getWhichTime() );

        return arg1;
    }
}
