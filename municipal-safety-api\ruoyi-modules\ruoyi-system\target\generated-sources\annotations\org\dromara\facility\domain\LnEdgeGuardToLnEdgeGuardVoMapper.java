package org.dromara.facility.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.bo.LnEdgeGuardBoToLnEdgeGuardMapper;
import org.dromara.facility.domain.vo.LnEdgeGuardVo;
import org.dromara.facility.domain.vo.LnEdgeGuardVoToLnEdgeGuardMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {LnEdgeGuardBoToLnEdgeGuardMapper.class,LnEdgeGuardVoToLnEdgeGuardMapper.class},
    imports = {}
)
public interface LnEdgeGuardToLnEdgeGuardVoMapper extends BaseMapper<LnEdgeGuard, LnEdgeGuardVo> {
}
