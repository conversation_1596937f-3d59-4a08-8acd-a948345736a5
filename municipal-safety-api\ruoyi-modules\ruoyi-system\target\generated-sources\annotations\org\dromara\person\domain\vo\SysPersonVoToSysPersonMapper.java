package org.dromara.person.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.person.domain.SysPerson;
import org.dromara.person.domain.SysPersonToSysPersonVoMapper;
import org.dromara.person.domain.SysQualificationToSysQualificationVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {SysQualificationVoToSysQualificationMapper.class,SysQualificationToSysQualificationVoMapper.class,SysPersonToSysPersonVoMapper.class},
    imports = {}
)
public interface SysPersonVoToSysPersonMapper extends BaseMapper<SysPersonVo, SysPerson> {
}
