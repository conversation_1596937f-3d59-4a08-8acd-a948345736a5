{"doc": " 项目录入业务对象 prj_projects\n\n <AUTHOR>\n @date 2025-05-07\n", "fields": [{"name": "projectId", "doc": " 项目ID\n"}, {"name": "projectName", "doc": " 项目名称\n"}, {"name": "projectCode", "doc": " 项目编码/标识\n"}, {"name": "projectOverview", "doc": " 工程概况 (对应附件一.1)\n"}, {"name": "constructionPermitNo", "doc": " 施工许可证编号 (对应附件一.2)\n"}, {"name": "constructionPermitDocId", "doc": " 施工许可证扫描件文档ID (逻辑外键至 sys_documents.document_id)\n"}, {"name": "lola", "doc": " 项目位置（经纬度）\n"}, {"name": "provinceCode", "doc": " 省/直辖市编码\n"}, {"name": "provinceName", "doc": " 省/直辖市名称\n"}, {"name": "cityCode", "doc": " 市编码\n"}, {"name": "cityName", "doc": " 市名称\n"}, {"name": "districtCode", "doc": " 区/县编码\n"}, {"name": "districtName", "doc": " 区/县名称\n"}, {"name": "countyCode", "doc": " 乡镇/街道编码 (可选)\n"}, {"name": "countyName", "doc": " 乡镇/街道名称 (可选)\n"}, {"name": "locationDetail", "doc": " 详细地址\n"}, {"name": "siteArea", "doc": " 占地面积(平方米)\n"}, {"name": "budgetTotal", "doc": " 预算投资总额(万元)\n"}, {"name": "supervisingQsOrgId", "doc": " 项目所属质监站机构ID\n"}, {"name": "status", "doc": " 项目状态\n"}, {"name": "startDate", "doc": " 计划开工日期\n"}, {"name": "plannedEndDate", "doc": " 计划竣工日期\n"}, {"name": "actualStartDate", "doc": " 实际开工日期\n"}, {"name": "actualEndDate", "doc": " 实际竣工日期\n"}, {"name": "clientOrgId", "doc": " 建设单位ID (逻辑外键至 sys_dept.dept_id)\n"}, {"name": "constructionOrgId", "doc": " 施工总包单位ID (逻辑外键至 sys_dept.dept_id)\n"}, {"name": "supervisionOrgId", "doc": " 监理单位ID (逻辑外键至 sys_dept.dept_id)\n"}, {"name": "designOrgId", "doc": " 设计单位ID (逻辑外键至 sys_dept.dept_id)\n"}, {"name": "surveyOrgId", "doc": " 勘察单位ID (逻辑外键至 sys_dept.dept_id)\n"}, {"name": "installationDismantlingOrgId", "doc": " 安拆单位ID (逻辑外键至 sys_dept.dept_id)\n"}, {"name": "maintenanceOrgId", "doc": " 维保单位ID (逻辑外键至 sys_dept.dept_id)\n"}, {"name": "subcontractorOrgIds", "doc": " 专业分包单位ID列表 (JSON数组，元素为 sys_organizations.org_id)\n"}, {"name": "projectManagerUserId", "doc": " 施工单位项目负责人ID (逻辑外键至 sys_users.user_id)\n"}, {"name": "supervisionChiefEngUserId", "doc": " 监理单位总监ID (逻辑外键至 sys_users.user_id)\n"}, {"name": "safetyMeasuresFeeDocId", "doc": " 危大工程安全防护文明施工措施费财务凭证文档ID (对应附件三.1)\n"}, {"name": "personIds", "doc": " 勾选的项目人员列表，逗号拼接\n"}, {"name": "type", "doc": "  type  移动端进入 分类\n"}, {"name": "projectIdList", "doc": "  projectIdList\n"}, {"name": "projectType", "doc": "  type  判断专家是否存在项目\n"}], "enumConstants": [], "methods": [], "constructors": []}