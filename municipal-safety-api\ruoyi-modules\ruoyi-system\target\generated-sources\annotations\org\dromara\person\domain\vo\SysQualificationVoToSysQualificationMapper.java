package org.dromara.person.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.person.domain.SysQualification;
import org.dromara.person.domain.SysQualificationToSysQualificationVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {SysQualificationToSysQualificationVoMapper.class},
    imports = {}
)
public interface SysQualificationVoToSysQualificationMapper extends BaseMapper<SysQualificationVo, SysQualification> {
}
