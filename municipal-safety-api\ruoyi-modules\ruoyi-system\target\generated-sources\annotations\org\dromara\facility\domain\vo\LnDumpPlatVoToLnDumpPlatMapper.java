package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.LnDumpPlat;
import org.dromara.facility.domain.LnDumpPlatToLnDumpPlatVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {LnDumpPlatToLnDumpPlatVoMapper.class},
    imports = {}
)
public interface LnDumpPlatVoToLnDumpPlatMapper extends BaseMapper<LnDumpPlatVo, LnDumpPlat> {
}
