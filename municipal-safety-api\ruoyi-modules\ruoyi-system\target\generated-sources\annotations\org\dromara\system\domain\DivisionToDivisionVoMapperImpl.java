package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.DivisionVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class DivisionToDivisionVoMapperImpl implements DivisionToDivisionVoMapper {

    @Override
    public DivisionVo convert(Division arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DivisionVo divisionVo = new DivisionVo();

        divisionVo.setDivisionCode( arg0.getDivisionCode() );
        divisionVo.setDivisionName( arg0.getDivisionName() );
        divisionVo.setLevel( arg0.getLevel() );
        divisionVo.setParentCode( arg0.getParentCode() );

        return divisionVo;
    }

    @Override
    public DivisionVo convert(Division arg0, DivisionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setDivisionCode( arg0.getDivisionCode() );
        arg1.setDivisionName( arg0.getDivisionName() );
        arg1.setLevel( arg0.getLevel() );
        arg1.setParentCode( arg0.getParentCode() );

        return arg1;
    }
}
