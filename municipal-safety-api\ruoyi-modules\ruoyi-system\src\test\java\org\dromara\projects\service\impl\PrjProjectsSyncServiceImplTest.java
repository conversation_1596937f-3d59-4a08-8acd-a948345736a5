package org.dromara.projects.service.impl;

import cn.hutool.core.util.StrUtil;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.projects.domain.bo.PrjProjectsBo;
import org.dromara.projects.domain.dto.sync.CjdwDto;
import org.dromara.projects.domain.dto.sync.SyncResponseDto;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.service.IPrjProjectsService;
import org.dromara.system.domain.SysDept;
import org.dromara.system.mapper.SysDeptMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * 项目同步服务测试类 - 专注于验证施工方用户权限控制
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@ExtendWith(MockitoExtension.class)
class PrjProjectsSyncServiceImplTest {

    @Mock
    private IPrjProjectsService prjProjectsService;

    @Mock
    private SysDeptMapper sysDeptMapper;

    @InjectMocks
    private PrjProjectsSyncServiceImpl syncService;

    private LoginUser constructionUser;
    private LoginUser adminUser;
    private SysDept constructionDept;
    private SysDept adminDept;

    @BeforeEach
    void setUp() {
        // 设置施工方用户
        constructionUser = new LoginUser();
        constructionUser.setUserId(1001L);
        constructionUser.setUsername("construction_user");
        constructionUser.setDeptId(2001L);

        // 设置管理员用户
        adminUser = new LoginUser();
        adminUser.setUserId(1002L);
        adminUser.setUsername("admin_user");
        adminUser.setDeptId(2002L);

        // 设置施工方部门
        constructionDept = new SysDept();
        constructionDept.setDeptId(2001L);
        constructionDept.setDeptName("测试施工公司");
        constructionDept.setDeptType("CONSTRUCTION");
        constructionDept.setDeptCode("91123456789012345X"); // 统一社会信用代码

        // 设置管理员部门
        adminDept = new SysDept();
        adminDept.setDeptId(2002L);
        adminDept.setDeptName("系统管理部门");
        adminDept.setDeptType("GOV_CITY");
        adminDept.setDeptCode("ADMIN001");
    }

    @Test
    void testValidateUserPermissionForSync_ConstructionUserWithValidProject_ShouldPass() {
        // 准备测试数据
        String constructionPermitNo = "TEST123456789";
        
        // 模拟现有项目属于当前施工方用户
        PrjProjectsVo existingProject = new PrjProjectsVo();
        existingProject.setProjectId(3001L);
        existingProject.setConstructionPermitNo(constructionPermitNo);
        existingProject.setConstructionOrgId(2001L); // 与用户部门ID相同
        
        List<PrjProjectsVo> existingProjects = List.of(existingProject);

        // 设置Mock行为
        when(sysDeptMapper.selectById(2001L)).thenReturn(constructionDept);
        when(prjProjectsService.queryList(any(PrjProjectsBo.class))).thenReturn(existingProjects);

        // 使用MockedStatic模拟LoginHelper
        try (MockedStatic<LoginHelper> loginHelperMock = mockStatic(LoginHelper.class)) {
            loginHelperMock.when(LoginHelper::getLoginUser).thenReturn(constructionUser);

            // 执行测试 - 通过反射调用私有方法
            R<String> result = invokeValidateUserPermissionForSync(constructionPermitNo);

            // 验证结果
            assertTrue(R.isSuccess(result));
            assertEquals("权限验证通过", result.getMsg());
        }
    }

    @Test
    void testValidateUserPermissionForSync_ConstructionUserWithInvalidProject_ShouldFail() {
        // 准备测试数据
        String constructionPermitNo = "TEST123456789";
        
        // 模拟现有项目属于其他施工方
        PrjProjectsVo existingProject = new PrjProjectsVo();
        existingProject.setProjectId(3001L);
        existingProject.setConstructionPermitNo(constructionPermitNo);
        existingProject.setConstructionOrgId(2999L); // 不同的部门ID
        
        List<PrjProjectsVo> existingProjects = List.of(existingProject);

        // 设置Mock行为
        when(sysDeptMapper.selectById(2001L)).thenReturn(constructionDept);
        when(prjProjectsService.queryList(any(PrjProjectsBo.class))).thenReturn(existingProjects);

        // 使用MockedStatic模拟LoginHelper
        try (MockedStatic<LoginHelper> loginHelperMock = mockStatic(LoginHelper.class)) {
            loginHelperMock.when(LoginHelper::getLoginUser).thenReturn(constructionUser);

            // 执行测试
            R<String> result = invokeValidateUserPermissionForSync(constructionPermitNo);

            // 验证结果
            assertFalse(R.isSuccess(result));
            assertTrue(result.getMsg().contains("无权限同步此项目"));
        }
    }

    @Test
    void testValidateUserPermissionForSync_AdminUser_ShouldPass() {
        // 准备测试数据
        String constructionPermitNo = "TEST123456789";

        // 设置Mock行为
        when(sysDeptMapper.selectById(2002L)).thenReturn(adminDept);

        // 使用MockedStatic模拟LoginHelper
        try (MockedStatic<LoginHelper> loginHelperMock = mockStatic(LoginHelper.class)) {
            loginHelperMock.when(LoginHelper::getLoginUser).thenReturn(adminUser);

            // 执行测试
            R<String> result = invokeValidateUserPermissionForSync(constructionPermitNo);

            // 验证结果
            assertTrue(R.isSuccess(result));
            assertEquals("权限验证通过", result.getMsg());
        }
    }

    @Test
    void testValidateUserPermissionForSync_NoLoginUser_ShouldFail() {
        // 准备测试数据
        String constructionPermitNo = "TEST123456789";

        // 使用MockedStatic模拟LoginHelper返回null
        try (MockedStatic<LoginHelper> loginHelperMock = mockStatic(LoginHelper.class)) {
            loginHelperMock.when(LoginHelper::getLoginUser).thenReturn(null);

            // 执行测试
            R<String> result = invokeValidateUserPermissionForSync(constructionPermitNo);

            // 验证结果
            assertFalse(R.isSuccess(result));
            assertTrue(result.getMsg().contains("用户未登录"));
        }
    }

    @Test
    void testIsConstructionCompanyUser_ConstructionDept_ShouldReturnTrue() {
        // 设置Mock行为
        when(sysDeptMapper.selectById(2001L)).thenReturn(constructionDept);

        // 执行测试
        boolean result = invokeIsConstructionCompanyUser(constructionUser);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsConstructionCompanyUser_NonConstructionDept_ShouldReturnFalse() {
        // 设置Mock行为
        when(sysDeptMapper.selectById(2002L)).thenReturn(adminDept);

        // 执行测试
        boolean result = invokeIsConstructionCompanyUser(adminUser);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testIsConstructionCompanyUser_MultiTypeDept_ShouldReturnTrue() {
        // 设置包含多种类型的部门
        SysDept multiTypeDept = new SysDept();
        multiTypeDept.setDeptId(2003L);
        multiTypeDept.setDeptType("CLIENT,CONSTRUCTION,SUPERVISION");

        LoginUser multiTypeUser = new LoginUser();
        multiTypeUser.setDeptId(2003L);

        // 设置Mock行为
        when(sysDeptMapper.selectById(2003L)).thenReturn(multiTypeDept);

        // 执行测试
        boolean result = invokeIsConstructionCompanyUser(multiTypeUser);

        // 验证结果
        assertTrue(result);
    }

    // 辅助方法：通过反射调用私有方法
    private R<String> invokeValidateUserPermissionForSync(String constructionPermitNo) {
        try {
            var method = PrjProjectsSyncServiceImpl.class.getDeclaredMethod("validateUserPermissionForSync", String.class);
            method.setAccessible(true);
            return (R<String>) method.invoke(syncService, constructionPermitNo);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke validateUserPermissionForSync", e);
        }
    }

    private boolean invokeIsConstructionCompanyUser(LoginUser loginUser) {
        try {
            var method = PrjProjectsSyncServiceImpl.class.getDeclaredMethod("isConstructionCompanyUser", LoginUser.class);
            method.setAccessible(true);
            return (boolean) method.invoke(syncService, loginUser);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke isConstructionCompanyUser", e);
        }
    }
}
