package org.dromara.ai.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.ai.domain.AiHazAnalysisTasksResult;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class AiHazAnalysisTasksResultVoToAiHazAnalysisTasksResultMapperImpl implements AiHazAnalysisTasksResultVoToAiHazAnalysisTasksResultMapper {

    @Override
    public AiHazAnalysisTasksResult convert(AiHazAnalysisTasksResultVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AiHazAnalysisTasksResult aiHazAnalysisTasksResult = new AiHazAnalysisTasksResult();

        aiHazAnalysisTasksResult.setResultId( arg0.getResultId() );
        aiHazAnalysisTasksResult.setTaskId( arg0.getTaskId() );
        aiHazAnalysisTasksResult.setViolation( arg0.getViolation() );
        aiHazAnalysisTasksResult.setRegulation( arg0.getRegulation() );
        aiHazAnalysisTasksResult.setCoordinate( arg0.getCoordinate() );
        aiHazAnalysisTasksResult.setLevel( arg0.getLevel() );
        aiHazAnalysisTasksResult.setMeasure( arg0.getMeasure() );

        return aiHazAnalysisTasksResult;
    }

    @Override
    public AiHazAnalysisTasksResult convert(AiHazAnalysisTasksResultVo arg0, AiHazAnalysisTasksResult arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setResultId( arg0.getResultId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setViolation( arg0.getViolation() );
        arg1.setRegulation( arg0.getRegulation() );
        arg1.setCoordinate( arg0.getCoordinate() );
        arg1.setLevel( arg0.getLevel() );
        arg1.setMeasure( arg0.getMeasure() );

        return arg1;
    }
}
