package org.dromara.facility.domain.bo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.MonitorFacility;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {},
    imports = {}
)
public interface MonitorFacilityBoToMonitorFacilityMapper extends BaseMapper<MonitorFacilityBo, MonitorFacility> {
}
