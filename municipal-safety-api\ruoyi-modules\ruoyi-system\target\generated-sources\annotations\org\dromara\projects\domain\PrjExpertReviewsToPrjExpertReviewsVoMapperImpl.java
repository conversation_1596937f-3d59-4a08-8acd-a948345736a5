package org.dromara.projects.domain;

import javax.annotation.processing.Generated;
import org.dromara.projects.domain.vo.PrjExpertReviewsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjExpertReviewsToPrjExpertReviewsVoMapperImpl implements PrjExpertReviewsToPrjExpertReviewsVoMapper {

    @Override
    public PrjExpertReviewsVo convert(PrjExpertReviews arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjExpertReviewsVo prjExpertReviewsVo = new PrjExpertReviewsVo();

        prjExpertReviewsVo.setReviewId( arg0.getReviewId() );
        prjExpertReviewsVo.setPlanId( arg0.getPlanId() );
        prjExpertReviewsVo.setReviewDate( arg0.getReviewDate() );
        prjExpertReviewsVo.setMeetingLocation( arg0.getMeetingLocation() );
        prjExpertReviewsVo.setConclusion( arg0.getConclusion() );
        prjExpertReviewsVo.setExpertOpinionSummary( arg0.getExpertOpinionSummary() );
        prjExpertReviewsVo.setConflictOfInterestWarning( arg0.getConflictOfInterestWarning() );
        prjExpertReviewsVo.setReportDocumentId( arg0.getReportDocumentId() );
        prjExpertReviewsVo.setConvenorUserId( arg0.getConvenorUserId() );

        return prjExpertReviewsVo;
    }

    @Override
    public PrjExpertReviewsVo convert(PrjExpertReviews arg0, PrjExpertReviewsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setReviewId( arg0.getReviewId() );
        arg1.setPlanId( arg0.getPlanId() );
        arg1.setReviewDate( arg0.getReviewDate() );
        arg1.setMeetingLocation( arg0.getMeetingLocation() );
        arg1.setConclusion( arg0.getConclusion() );
        arg1.setExpertOpinionSummary( arg0.getExpertOpinionSummary() );
        arg1.setConflictOfInterestWarning( arg0.getConflictOfInterestWarning() );
        arg1.setReportDocumentId( arg0.getReportDocumentId() );
        arg1.setConvenorUserId( arg0.getConvenorUserId() );

        return arg1;
    }
}
