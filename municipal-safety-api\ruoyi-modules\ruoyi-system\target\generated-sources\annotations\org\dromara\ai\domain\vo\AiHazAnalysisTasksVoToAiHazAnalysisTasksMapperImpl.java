package org.dromara.ai.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.ai.domain.AiHazAnalysisTasks;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class AiHazAnalysisTasksVoToAiHazAnalysisTasksMapperImpl implements AiHazAnalysisTasksVoToAiHazAnalysisTasksMapper {

    @Override
    public AiHazAnalysisTasks convert(AiHazAnalysisTasksVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AiHazAnalysisTasks aiHazAnalysisTasks = new AiHazAnalysisTasks();

        aiHazAnalysisTasks.setTaskId( arg0.getTaskId() );
        aiHazAnalysisTasks.setProjectId( arg0.getProjectId() );
        aiHazAnalysisTasks.setItemId( arg0.getItemId() );
        aiHazAnalysisTasks.setSourceType( arg0.getSourceType() );
        aiHazAnalysisTasks.setExpertUserId( arg0.getExpertUserId() );
        aiHazAnalysisTasks.setUploadTime( arg0.getUploadTime() );
        aiHazAnalysisTasks.setPhotoDocumentId( arg0.getPhotoDocumentId() );
        aiHazAnalysisTasks.setGpsLocation( arg0.getGpsLocation() );
        aiHazAnalysisTasks.setLocationDescription( arg0.getLocationDescription() );
        aiHazAnalysisTasks.setAiPhotoDocumentId( arg0.getAiPhotoDocumentId() );
        aiHazAnalysisTasks.setAiRecognitionRawResult( arg0.getAiRecognitionRawResult() );
        aiHazAnalysisTasks.setStatus( arg0.getStatus() );
        aiHazAnalysisTasks.setRecheckStatus( arg0.getRecheckStatus() );
        aiHazAnalysisTasks.setRelatedWorkOrderId( arg0.getRelatedWorkOrderId() );

        return aiHazAnalysisTasks;
    }

    @Override
    public AiHazAnalysisTasks convert(AiHazAnalysisTasksVo arg0, AiHazAnalysisTasks arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setTaskId( arg0.getTaskId() );
        arg1.setProjectId( arg0.getProjectId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setSourceType( arg0.getSourceType() );
        arg1.setExpertUserId( arg0.getExpertUserId() );
        arg1.setUploadTime( arg0.getUploadTime() );
        arg1.setPhotoDocumentId( arg0.getPhotoDocumentId() );
        arg1.setGpsLocation( arg0.getGpsLocation() );
        arg1.setLocationDescription( arg0.getLocationDescription() );
        arg1.setAiPhotoDocumentId( arg0.getAiPhotoDocumentId() );
        arg1.setAiRecognitionRawResult( arg0.getAiRecognitionRawResult() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRecheckStatus( arg0.getRecheckStatus() );
        arg1.setRelatedWorkOrderId( arg0.getRelatedWorkOrderId() );

        return arg1;
    }
}
