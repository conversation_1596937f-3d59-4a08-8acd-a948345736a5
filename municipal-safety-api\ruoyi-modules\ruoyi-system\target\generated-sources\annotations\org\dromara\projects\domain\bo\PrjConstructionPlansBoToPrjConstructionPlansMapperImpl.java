package org.dromara.projects.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.projects.domain.PrjConstructionPlans;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjConstructionPlansBoToPrjConstructionPlansMapperImpl implements PrjConstructionPlansBoToPrjConstructionPlansMapper {

    @Override
    public PrjConstructionPlans convert(PrjConstructionPlansBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjConstructionPlans prjConstructionPlans = new PrjConstructionPlans();

        prjConstructionPlans.setSearchValue( arg0.getSearchValue() );
        prjConstructionPlans.setCreateDept( arg0.getCreateDept() );
        prjConstructionPlans.setCreateBy( arg0.getCreateBy() );
        prjConstructionPlans.setCreateTime( arg0.getCreateTime() );
        prjConstructionPlans.setUpdateBy( arg0.getUpdateBy() );
        prjConstructionPlans.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            prjConstructionPlans.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        prjConstructionPlans.setPlanId( arg0.getPlanId() );
        prjConstructionPlans.setItemId( arg0.getItemId() );
        prjConstructionPlans.setPlanName( arg0.getPlanName() );
        prjConstructionPlans.setPlanVersion( arg0.getPlanVersion() );
        prjConstructionPlans.setPlanDocumentId( arg0.getPlanDocumentId() );
        prjConstructionPlans.setApprovalFormDocId( arg0.getApprovalFormDocId() );
        prjConstructionPlans.setReviewStatus( arg0.getReviewStatus() );
        prjConstructionPlans.setAiDefectWarningDetails( arg0.getAiDefectWarningDetails() );

        return prjConstructionPlans;
    }

    @Override
    public PrjConstructionPlans convert(PrjConstructionPlansBo arg0, PrjConstructionPlans arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setPlanId( arg0.getPlanId() );
        arg1.setItemId( arg0.getItemId() );
        arg1.setPlanName( arg0.getPlanName() );
        arg1.setPlanVersion( arg0.getPlanVersion() );
        arg1.setPlanDocumentId( arg0.getPlanDocumentId() );
        arg1.setApprovalFormDocId( arg0.getApprovalFormDocId() );
        arg1.setReviewStatus( arg0.getReviewStatus() );
        arg1.setAiDefectWarningDetails( arg0.getAiDefectWarningDetails() );

        return arg1;
    }
}
