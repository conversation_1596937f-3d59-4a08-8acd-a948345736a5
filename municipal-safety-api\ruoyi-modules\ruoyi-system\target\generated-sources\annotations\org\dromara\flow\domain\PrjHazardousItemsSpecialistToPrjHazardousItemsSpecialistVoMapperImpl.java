package org.dromara.flow.domain;

import javax.annotation.processing.Generated;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialistVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjHazardousItemsSpecialistToPrjHazardousItemsSpecialistVoMapperImpl implements PrjHazardousItemsSpecialistToPrjHazardousItemsSpecialistVoMapper {

    @Override
    public PrjHazardousItemsSpecialistVo convert(PrjHazardousItemsSpecialist arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjHazardousItemsSpecialistVo prjHazardousItemsSpecialistVo = new PrjHazardousItemsSpecialistVo();

        prjHazardousItemsSpecialistVo.setId( arg0.getId() );
        prjHazardousItemsSpecialistVo.setSpecialist( arg0.getSpecialist() );
        prjHazardousItemsSpecialistVo.setInstruction( arg0.getInstruction() );
        prjHazardousItemsSpecialistVo.setDownPushFile( arg0.getDownPushFile() );
        prjHazardousItemsSpecialistVo.setTaskId( arg0.getTaskId() );

        return prjHazardousItemsSpecialistVo;
    }

    @Override
    public PrjHazardousItemsSpecialistVo convert(PrjHazardousItemsSpecialist arg0, PrjHazardousItemsSpecialistVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setSpecialist( arg0.getSpecialist() );
        arg1.setInstruction( arg0.getInstruction() );
        arg1.setDownPushFile( arg0.getDownPushFile() );
        arg1.setTaskId( arg0.getTaskId() );

        return arg1;
    }
}
