package org.dromara.attendance.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.attendance.domain.MAttPerson;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class MAttPersonVoToMAttPersonMapperImpl implements MAttPersonVoToMAttPersonMapper {

    @Override
    public MAttPerson convert(MAttPersonVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MAttPerson mAttPerson = new MAttPerson();

        mAttPerson.setId( arg0.getId() );
        mAttPerson.setPersonId( arg0.getPersonId() );
        mAttPerson.setSnId( arg0.getSnId() );

        return mAttPerson;
    }

    @Override
    public MAttPerson convert(MAttPersonVo arg0, MAttPerson arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setPersonId( arg0.getPersonId() );
        arg1.setSnId( arg0.getSnId() );

        return arg1;
    }
}
