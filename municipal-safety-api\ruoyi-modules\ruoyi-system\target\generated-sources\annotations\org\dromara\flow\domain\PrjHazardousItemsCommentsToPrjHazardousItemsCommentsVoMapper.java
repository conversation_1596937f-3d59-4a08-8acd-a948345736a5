package org.dromara.flow.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.bo.PrjHazardousItemsCommentsBoToPrjHazardousItemsCommentsMapper;
import org.dromara.flow.domain.bo.PrjHazardousItemsFileBoToPrjHazardousItemsFileMapper;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBoToPrjHazardousItemsSpecialWarningMapper;
import org.dromara.flow.domain.vo.PrjHazardousItemsCommentsVo;
import org.dromara.flow.domain.vo.PrjHazardousItemsCommentsVoToPrjHazardousItemsCommentsMapper;
import org.dromara.flow.domain.vo.PrjHazardousItemsFileVoToPrjHazardousItemsFileMapper;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialWarningVoToPrjHazardousItemsSpecialWarningMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {PrjHazardousItemsFileBoToPrjHazardousItemsFileMapper.class,PrjHazardousItemsFileVoToPrjHazardousItemsFileMapper.class,PrjHazardousItemsFileToPrjHazardousItemsFileVoMapper.class,PrjHazardousItemsSpecialWarningVoToPrjHazardousItemsSpecialWarningMapper.class,PrjHazardousItemsSpecialWarningBoToPrjHazardousItemsSpecialWarningMapper.class,PrjHazardousItemsSpecialWarningToPrjHazardousItemsSpecialWarningVoMapper.class,PrjHazardousItemsCommentsVoToPrjHazardousItemsCommentsMapper.class,PrjHazardousItemsCommentsBoToPrjHazardousItemsCommentsMapper.class},
    imports = {}
)
public interface PrjHazardousItemsCommentsToPrjHazardousItemsCommentsVoMapper extends BaseMapper<PrjHazardousItemsComments, PrjHazardousItemsCommentsVo> {
}
