package org.dromara.plan.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.plan.domain.PrjPatrolPlan;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjPatrolPlanVoToPrjPatrolPlanMapperImpl implements PrjPatrolPlanVoToPrjPatrolPlanMapper {

    @Override
    public PrjPatrolPlan convert(PrjPatrolPlanVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjPatrolPlan prjPatrolPlan = new PrjPatrolPlan();

        prjPatrolPlan.setPlanId( arg0.getPlanId() );
        prjPatrolPlan.setPlanName( arg0.getPlanName() );
        prjPatrolPlan.setBeginTime( arg0.getBeginTime() );
        prjPatrolPlan.setEndTime( arg0.getEndTime() );
        prjPatrolPlan.setDeptIds( arg0.getDeptIds() );
        prjPatrolPlan.setProjectIds( arg0.getProjectIds() );
        prjPatrolPlan.setExpertIds( arg0.getExpertIds() );
        prjPatrolPlan.setRemarks( arg0.getRemarks() );

        return prjPatrolPlan;
    }

    @Override
    public PrjPatrolPlan convert(PrjPatrolPlanVo arg0, PrjPatrolPlan arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setPlanId( arg0.getPlanId() );
        arg1.setPlanName( arg0.getPlanName() );
        arg1.setBeginTime( arg0.getBeginTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setDeptIds( arg0.getDeptIds() );
        arg1.setProjectIds( arg0.getProjectIds() );
        arg1.setExpertIds( arg0.getExpertIds() );
        arg1.setRemarks( arg0.getRemarks() );

        return arg1;
    }
}
