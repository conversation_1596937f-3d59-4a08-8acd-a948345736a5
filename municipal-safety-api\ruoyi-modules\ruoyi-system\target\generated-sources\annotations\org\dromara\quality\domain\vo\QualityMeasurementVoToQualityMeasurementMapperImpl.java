package org.dromara.quality.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.quality.domain.QualityMeasurement;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:45+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class QualityMeasurementVoToQualityMeasurementMapperImpl implements QualityMeasurementVoToQualityMeasurementMapper {

    @Override
    public QualityMeasurement convert(QualityMeasurementVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QualityMeasurement qualityMeasurement = new QualityMeasurement();

        qualityMeasurement.setCreateBy( arg0.getCreateBy() );
        qualityMeasurement.setCreateTime( arg0.getCreateTime() );
        qualityMeasurement.setUpdateBy( arg0.getUpdateBy() );
        qualityMeasurement.setUpdateTime( arg0.getUpdateTime() );
        qualityMeasurement.setMeasurementId( arg0.getMeasurementId() );
        qualityMeasurement.setMeasurementTime( arg0.getMeasurementTime() );
        qualityMeasurement.setMeasurementItem( arg0.getMeasurementItem() );
        qualityMeasurement.setDeviceId( arg0.getDeviceId() );
        qualityMeasurement.setDeviceName( arg0.getDeviceName() );
        qualityMeasurement.setDeviceCode( arg0.getDeviceCode() );
        qualityMeasurement.setMeasurementResult( arg0.getMeasurementResult() );
        qualityMeasurement.setIsCompliant( arg0.getIsCompliant() );
        qualityMeasurement.setProjectName( arg0.getProjectName() );
        qualityMeasurement.setMeasurementLocation( arg0.getMeasurementLocation() );
        qualityMeasurement.setStandardValue( arg0.getStandardValue() );
        qualityMeasurement.setDeviationValue( arg0.getDeviationValue() );
        qualityMeasurement.setMeasurementPerson( arg0.getMeasurementPerson() );
        qualityMeasurement.setIsHazardMarked( arg0.getIsHazardMarked() );
        qualityMeasurement.setHazardDescription( arg0.getHazardDescription() );
        qualityMeasurement.setStatus( arg0.getStatus() );
        qualityMeasurement.setRemark( arg0.getRemark() );

        return qualityMeasurement;
    }

    @Override
    public QualityMeasurement convert(QualityMeasurementVo arg0, QualityMeasurement arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setMeasurementId( arg0.getMeasurementId() );
        arg1.setMeasurementTime( arg0.getMeasurementTime() );
        arg1.setMeasurementItem( arg0.getMeasurementItem() );
        arg1.setDeviceId( arg0.getDeviceId() );
        arg1.setDeviceName( arg0.getDeviceName() );
        arg1.setDeviceCode( arg0.getDeviceCode() );
        arg1.setMeasurementResult( arg0.getMeasurementResult() );
        arg1.setIsCompliant( arg0.getIsCompliant() );
        arg1.setProjectName( arg0.getProjectName() );
        arg1.setMeasurementLocation( arg0.getMeasurementLocation() );
        arg1.setStandardValue( arg0.getStandardValue() );
        arg1.setDeviationValue( arg0.getDeviationValue() );
        arg1.setMeasurementPerson( arg0.getMeasurementPerson() );
        arg1.setIsHazardMarked( arg0.getIsHazardMarked() );
        arg1.setHazardDescription( arg0.getHazardDescription() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
