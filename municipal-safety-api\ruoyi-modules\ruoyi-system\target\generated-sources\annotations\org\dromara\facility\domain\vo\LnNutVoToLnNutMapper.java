package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.LnNut;
import org.dromara.facility.domain.LnNutToLnNutVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {LnNutToLnNutVoMapper.class},
    imports = {}
)
public interface LnNutVoToLnNutMapper extends BaseMapper<LnNutVo, LnNut> {
}
