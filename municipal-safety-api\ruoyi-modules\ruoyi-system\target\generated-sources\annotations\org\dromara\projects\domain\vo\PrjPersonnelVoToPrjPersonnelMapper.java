package org.dromara.projects.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.attendance.domain.MAttSnToMAttSnVoMapper;
import org.dromara.attendance.domain.vo.MAttSnVoToMAttSnMapper;
import org.dromara.projects.domain.PrjPersonnel;
import org.dromara.projects.domain.PrjPersonnelToPrjPersonnelVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {MAttSnVoToMAttSnMapper.class,MAttSnToMAttSnVoMapper.class,PrjPersonnelToPrjPersonnelVoMapper.class},
    imports = {}
)
public interface PrjPersonnelVoToPrjPersonnelMapper extends BaseMapper<PrjPersonnelVo, PrjPersonnel> {
}
