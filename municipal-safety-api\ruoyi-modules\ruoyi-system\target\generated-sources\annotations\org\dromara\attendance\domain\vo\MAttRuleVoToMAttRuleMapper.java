package org.dromara.attendance.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.attendance.domain.MAttRule;
import org.dromara.attendance.domain.MAttRuleToMAttRuleVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {MAttRuleToMAttRuleVoMapper.class},
    imports = {}
)
public interface MAttRuleVoToMAttRuleMapper extends BaseMapper<MAttRuleVo, MAttRule> {
}
