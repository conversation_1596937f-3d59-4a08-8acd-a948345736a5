package org.dromara.quality.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.quality.domain.QualityMeasurement;
import org.dromara.quality.domain.QualityMeasurementToQualityMeasurementVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {QualityMeasurementToQualityMeasurementVoMapper.class},
    imports = {}
)
public interface QualityMeasurementVoToQualityMeasurementMapper extends BaseMapper<QualityMeasurementVo, QualityMeasurement> {
}
