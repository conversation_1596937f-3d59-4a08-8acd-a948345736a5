package org.dromara.facility.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.facility.domain.LnSmoke;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class LnSmokeVoToLnSmokeMapperImpl implements LnSmokeVoToLnSmokeMapper {

    @Override
    public LnSmoke convert(LnSmokeVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LnSmoke lnSmoke = new LnSmoke();

        lnSmoke.setId( arg0.getId() );
        lnSmoke.setDeviceNo( arg0.getDeviceNo() );
        lnSmoke.setDeviceName( arg0.getDeviceName() );
        lnSmoke.setEventCode( arg0.getEventCode() );
        lnSmoke.setEventContent( arg0.getEventContent() );
        lnSmoke.setEventTime( arg0.getEventTime() );
        lnSmoke.setDevNo( arg0.getDevNo() );
        lnSmoke.setCreateTime( arg0.getCreateTime() );

        return lnSmoke;
    }

    @Override
    public LnSmoke convert(LnSmokeVo arg0, LnSmoke arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDeviceNo( arg0.getDeviceNo() );
        arg1.setDeviceName( arg0.getDeviceName() );
        arg1.setEventCode( arg0.getEventCode() );
        arg1.setEventContent( arg0.getEventContent() );
        arg1.setEventTime( arg0.getEventTime() );
        arg1.setDevNo( arg0.getDevNo() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
