package org.dromara.flow.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.flow.domain.bo.PrjHazardousItemsSpecialWarningBoToPrjHazardousItemsSpecialWarningMapper;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialWarningVo;
import org.dromara.flow.domain.vo.PrjHazardousItemsSpecialWarningVoToPrjHazardousItemsSpecialWarningMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {PrjHazardousItemsSpecialWarningVoToPrjHazardousItemsSpecialWarningMapper.class,PrjHazardousItemsSpecialWarningBoToPrjHazardousItemsSpecialWarningMapper.class},
    imports = {}
)
public interface PrjHazardousItemsSpecialWarningToPrjHazardousItemsSpecialWarningVoMapper extends BaseMapper<PrjHazardousItemsSpecialWarning, PrjHazardousItemsSpecialWarningVo> {
}
