package org.dromara.plan.domain;

import javax.annotation.processing.Generated;
import org.dromara.plan.domain.vo.PrjPatrolPlanVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T19:51:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (BellSoft)"
)
@Component
public class PrjPatrolPlanToPrjPatrolPlanVoMapperImpl implements PrjPatrolPlanToPrjPatrolPlanVoMapper {

    @Override
    public PrjPatrolPlanVo convert(PrjPatrolPlan arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PrjPatrolPlanVo prjPatrolPlanVo = new PrjPatrolPlanVo();

        prjPatrolPlanVo.setPlanId( arg0.getPlanId() );
        prjPatrolPlanVo.setPlanName( arg0.getPlanName() );
        prjPatrolPlanVo.setBeginTime( arg0.getBeginTime() );
        prjPatrolPlanVo.setEndTime( arg0.getEndTime() );
        prjPatrolPlanVo.setDeptIds( arg0.getDeptIds() );
        prjPatrolPlanVo.setProjectIds( arg0.getProjectIds() );
        prjPatrolPlanVo.setExpertIds( arg0.getExpertIds() );
        prjPatrolPlanVo.setRemarks( arg0.getRemarks() );

        return prjPatrolPlanVo;
    }

    @Override
    public PrjPatrolPlanVo convert(PrjPatrolPlan arg0, PrjPatrolPlanVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setPlanId( arg0.getPlanId() );
        arg1.setPlanName( arg0.getPlanName() );
        arg1.setBeginTime( arg0.getBeginTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setDeptIds( arg0.getDeptIds() );
        arg1.setProjectIds( arg0.getProjectIds() );
        arg1.setExpertIds( arg0.getExpertIds() );
        arg1.setRemarks( arg0.getRemarks() );

        return arg1;
    }
}
