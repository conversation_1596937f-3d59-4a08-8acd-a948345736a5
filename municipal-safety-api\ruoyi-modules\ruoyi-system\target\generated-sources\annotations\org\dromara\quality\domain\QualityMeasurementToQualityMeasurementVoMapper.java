package org.dromara.quality.domain;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.quality.domain.bo.QualityMeasurementBoToQualityMeasurementMapper;
import org.dromara.quality.domain.vo.QualityMeasurementVo;
import org.dromara.quality.domain.vo.QualityMeasurementVoToQualityMeasurementMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {QualityMeasurementBoToQualityMeasurementMapper.class,QualityMeasurementVoToQualityMeasurementMapper.class},
    imports = {}
)
public interface QualityMeasurementToQualityMeasurementVoMapper extends BaseMapper<QualityMeasurement, QualityMeasurementVo> {
}
