package org.dromara.facility.domain.vo;

import io.github.linpeilie.AutoMapperConfig__436;
import io.github.linpeilie.BaseMapper;
import org.dromara.facility.domain.JlDustReal;
import org.dromara.facility.domain.JlDustRealToJlDustRealVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__436.class,
    uses = {JlDustRealToJlDustRealVoMapper.class},
    imports = {}
)
public interface JlDustRealVoToJlDustRealMapper extends BaseMapper<JlDustRealVo, JlDustReal> {
}
